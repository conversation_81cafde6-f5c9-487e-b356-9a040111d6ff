/**
 * KOPEGBI POS Main JavaScript
 */

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Confirm delete actions
    $(document).on('click', '.btn-delete', function(e) {
        e.preventDefault();
        var href = $(this).attr('href');
        var message = $(this).data('message') || '<PERSON><PERSON>kah Anda yakin ingin menghapus data ini?';

        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: message,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = href;
            }
        });
    });

    // Format number inputs as currency
    $(document).on('input', '.currency-input', function() {
        var value = $(this).val().replace(/[^0-9]/g, '');
        if (value) {
            $(this).val(formatNumber(value));
        }
    });

    // Format number inputs
    $(document).on('input', '.number-input', function() {
        var value = $(this).val().replace(/[^0-9]/g, '');
        $(this).val(value);
    });

    // Auto-calculate in forms
    $(document).on('input', '.auto-calculate', function() {
        calculateTotal();
    });
});

// Format number with thousand separator
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

// Parse formatted number back to integer
function parseNumber(str) {
    return parseInt(str.replace(/\./g, '')) || 0;
}

// Format currency (Rupiah)
function formatRupiah(amount) {
    return 'Rp ' + formatNumber(amount);
}

// Show loading spinner
function showLoading(element) {
    if (element) {
        element.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
        element.prop('disabled', true);
    }
}

// Hide loading spinner
function hideLoading(element, originalText) {
    if (element) {
        element.html(originalText);
        element.prop('disabled', false);
    }
}

// AJAX helper function
function ajaxRequest(url, data, successCallback, errorCallback) {
    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                if (successCallback) successCallback(response);
            } else {
                if (errorCallback) {
                    errorCallback(response.message || 'Terjadi kesalahan');
                } else {
                    showError(response.message || 'Terjadi kesalahan');
                }
            }
        },
        error: function(xhr, status, error) {
            if (errorCallback) {
                errorCallback('Terjadi kesalahan koneksi');
            } else {
                showError('Terjadi kesalahan koneksi');
            }
        }
    });
}

// DataTable default configuration
function initDataTable(selector, options = {}) {
    var defaultOptions = {
        processing: true,
        serverSide: true,
        responsive: true,
        language: {
            "decimal": "",
            "emptyTable": "Tidak ada data yang tersedia",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
            "infoFiltered": "(disaring dari _MAX_ total entri)",
            "infoPostFix": "",
            "thousands": ".",
            "lengthMenu": "Tampilkan _MENU_ entri",
            "loadingRecords": "Memuat...",
            "processing": "Memproses...",
            "search": "Cari:",
            "zeroRecords": "Tidak ditemukan data yang sesuai",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        ...options
    };

    return $(selector).DataTable(defaultOptions);
}

// Form validation helper
function validateForm(formSelector) {
    var isValid = true;
    $(formSelector + ' [required]').each(function() {
        if (!$(this).val()) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    return isValid;
}

// Reset form
function resetForm(formSelector) {
    $(formSelector)[0].reset();
    $(formSelector + ' .is-invalid').removeClass('is-invalid');
    $(formSelector + ' .is-valid').removeClass('is-valid');
}

// Print function
function printElement(elementId) {
    var printContents = document.getElementById(elementId).innerHTML;
    var originalContents = document.body.innerHTML;

    document.body.innerHTML = printContents;
    window.print();
    document.body.innerHTML = originalContents;
    location.reload();
}

// Export to Excel (requires SheetJS)
function exportToExcel(tableId, filename) {
    if (typeof XLSX !== 'undefined') {
        var wb = XLSX.utils.table_to_book(document.getElementById(tableId));
        XLSX.writeFile(wb, filename + '.xlsx');
    } else {
        showError('Library export tidak tersedia');
    }
}

// Barcode scanner functions (will be implemented later)
function startBarcodeScanner(callback) {
    // Implementation will be added when barcode feature is developed
    console.log('Barcode scanner not implemented yet');
}

function stopBarcodeScanner() {
    // Implementation will be added when barcode feature is developed
    console.log('Barcode scanner not implemented yet');
}
