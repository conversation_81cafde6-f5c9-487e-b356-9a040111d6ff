<?php
$page_title = 'Master Kategori';
require_once '../config/database.php';
require_once '../includes/header.php';

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $kd_kategori = (int)$_GET['id'];
    $connection = getConnection();
    
    // Check if kategori is used in barang
    $check_query = "SELECT COUNT(*) as count FROM barang WHERE kd_kategori = ?";
    $check_stmt = $connection->prepare($check_query);
    $check_stmt->bind_param("i", $kd_kategori);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $count = $check_result->fetch_assoc()['count'];
    
    if ($count > 0) {
        $error_message = "Kategori tidak dapat dihapus karena masih digunakan pada $count barang.";
    } else {
        $delete_query = "DELETE FROM kategori WHERE kd_kategori = ?";
        $delete_stmt = $connection->prepare($delete_query);
        $delete_stmt->bind_param("i", $kd_kategori);
        
        if ($delete_stmt->execute()) {
            $success_message = "Kategori berhasil dihapus.";
        } else {
            $error_message = "Gagal menghapus kategori.";
        }
        $delete_stmt->close();
    }
    
    $check_stmt->close();
    closeConnection($connection);
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tags me-2"></i>Master Kategori
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalKategori">
                <i class="fas fa-plus me-2"></i>Tambah Kategori
            </button>
        </div>
    </div>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Data Kategori</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="tableKategori" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="10%">No</th>
                                <th width="70%">Nama Kategori</th>
                                <th width="20%">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via DataTables AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Kategori -->
<div class="modal fade" id="modalKategori" tabindex="-1" aria-labelledby="modalKategoriLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalKategoriLabel">
                    <i class="fas fa-tags me-2"></i>Tambah Kategori
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formKategori">
                <div class="modal-body">
                    <input type="hidden" id="kd_kategori" name="kd_kategori">
                    <input type="hidden" id="action" name="action" value="add">
                    
                    <div class="mb-3">
                        <label for="nama_kategori" class="form-label">Nama Kategori <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nama_kategori" name="nama_kategori" required maxlength="50">
                        <div class="invalid-feedback">
                            Nama kategori harus diisi.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="btnSimpan">
                        <i class="fas fa-save me-2"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$page_scripts = ['kategori/js/kategori.js'];
require_once '../includes/footer.php';
?>
