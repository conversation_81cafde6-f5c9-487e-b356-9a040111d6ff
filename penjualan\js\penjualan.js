/**
 * Penjualan Transaction JavaScript
 */

// Global variables
let transaksiItems = [];
let itemCounter = 0;

$(document).ready(function() {
    // Initialize
    initializePenjualan();

    // Barcode input handler
    $('#barcode_input').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            cariBarangByBarcode();
        }
    });

    // Search button handler
    $('#btnCariBarang').click(function() {
        if ($('#barcode_input').val().trim()) {
            cariBarangByBarcode();
        } else {
            showModalCariBarang();
        }
    });

    // Barcode scanner button
    $('#btnScanBarcode').click(function() {
        showModalBarcodeScanner();
    });

    // Jumlah input handler
    $('#jumlah_input').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            cariBarangByBarcode();
        }
    });

    // Payment calculation
    $('#dibayar').on('input', function() {
        calculateKembalian();
    });

    // Save transaction button
    $('#btnSimpanTransaksi').click(function() {
        simpanTransaksi();
    });

    // Reset transaction button
    $('#btnResetTransaksi').click(function() {
        resetTransaksi();
    });

    // Search barang in modal
    $('#search_barang').on('input', function() {
        loadBarangModal();
    });
});

function initializePenjualan() {
    // Focus on barcode input
    $('#barcode_input').focus();

    // Initialize totals
    updateTotals();
}

function cariBarangByBarcode() {
    const barcode = $('#barcode_input').val().trim();
    const jumlah = parseInt($('#jumlah_input').val()) || 1;

    if (!barcode) {
        showError('Silakan scan barcode atau ketik nama barang.');
        return;
    }

    // Show loading
    $('#btnCariBarang').html('<i class="fas fa-spinner fa-spin"></i>');

    ajaxRequest(
        'ajax/penjualan_ajax.php',
        {
            action: 'get_barang_by_barcode',
            barcode: barcode
        },
        function(response) {
            // Success callback
            $('#btnCariBarang').html('<i class="fas fa-search"></i>');

            if (response.data) {
                addItemToTransaction(response.data, jumlah);
                $('#barcode_input').val('').focus();
                $('#jumlah_input').val(1);
            }
        },
        function(message) {
            // Error callback
            $('#btnCariBarang').html('<i class="fas fa-search"></i>');

            // If exact barcode not found, try searching by name
            if (barcode.length > 2) {
                showModalCariBarang(barcode);
            } else {
                showError(message);
            }
        }
    );
}

function addItemToTransaction(barang, jumlah = 1) {
    // Check if item already exists
    const existingIndex = transaksiItems.findIndex(item => item.kd_barang === barang.kd_barang);

    if (existingIndex !== -1) {
        // Update existing item
        const newJumlah = transaksiItems[existingIndex].jumlah + jumlah;

        if (newJumlah > barang.stock) {
            showError(`Stock tidak mencukupi. Stock tersedia: ${barang.stock}`);
            return;
        }

        transaksiItems[existingIndex].jumlah = newJumlah;
        updateItemRow(existingIndex);
    } else {
        // Add new item
        if (jumlah > barang.stock) {
            showError(`Stock tidak mencukupi. Stock tersedia: ${barang.stock}`);
            return;
        }

        const newItem = {
            id: ++itemCounter,
            kd_barang: barang.kd_barang,
            barcode: barang.barcode,
            nama_barang: barang.nama_barang,
            harga_beli: parseInt(barang.harga_beli || 0),
            harga_jual: parseInt(barang.harga_jual),
            jumlah: jumlah,
            disc: 0,
            stock: barang.stock,
            nama_satuan: barang.nama_satuan
        };

        transaksiItems.push(newItem);
        addItemRow(newItem);
    }

    updateTotals();
    showSuccess(`${barang.nama_barang} ditambahkan ke transaksi.`);
}

function addItemRow(item) {
    // Remove empty row if exists
    $('#empty_row').remove();

    const subtotal = (item.harga_jual * item.jumlah) - (item.harga_jual * item.jumlah * item.disc / 100);

    const row = `
        <tr id="item_${item.id}">
            <td>${transaksiItems.length}</td>
            <td>${item.barcode}</td>
            <td>${item.nama_barang}</td>
            <td>${formatRupiah(item.harga_jual)}</td>
            <td>
                <input type="number" class="form-control form-control-sm qty-input"
                       value="${item.jumlah}" min="1" max="${item.stock}"
                       data-id="${item.id}" style="width: 70px;">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm disc-input"
                       value="${item.disc}" min="0" max="100"
                       data-id="${item.id}" style="width: 70px;">
            </td>
            <td class="subtotal">${formatRupiah(subtotal)}</td>
            <td>
                <button type="button" class="btn btn-sm btn-danger btn-remove" data-id="${item.id}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;

    $('#transaksi_items').append(row);

    // Bind events for new row
    bindItemEvents();
}

function updateItemRow(index) {
    const item = transaksiItems[index];
    const subtotal = (item.harga_jual * item.jumlah) - (item.harga_jual * item.jumlah * item.disc / 100);

    const row = $(`#item_${item.id}`);
    row.find('.qty-input').val(item.jumlah);
    row.find('.disc-input').val(item.disc);
    row.find('.subtotal').text(formatRupiah(subtotal));
}

function bindItemEvents() {
    // Quantity change
    $('.qty-input').off('change').on('change', function() {
        const id = parseInt($(this).data('id'));
        const newQty = parseInt($(this).val()) || 1;
        const itemIndex = transaksiItems.findIndex(item => item.id === id);

        if (itemIndex !== -1) {
            const item = transaksiItems[itemIndex];

            if (newQty > item.stock) {
                showError(`Stock tidak mencukupi. Stock tersedia: ${item.stock}`);
                $(this).val(item.jumlah);
                return;
            }

            if (newQty <= 0) {
                $(this).val(1);
                return;
            }

            item.jumlah = newQty;
            updateItemRow(itemIndex);
            updateTotals();
        }
    });

    // Discount change
    $('.disc-input').off('change').on('change', function() {
        const id = parseInt($(this).data('id'));
        const newDisc = parseInt($(this).val()) || 0;
        const itemIndex = transaksiItems.findIndex(item => item.id === id);

        if (itemIndex !== -1) {
            if (newDisc < 0 || newDisc > 100) {
                showError('Diskon harus antara 0-100%');
                $(this).val(transaksiItems[itemIndex].disc);
                return;
            }

            transaksiItems[itemIndex].disc = newDisc;
            updateItemRow(itemIndex);
            updateTotals();
        }
    });

    // Remove item
    $('.btn-remove').off('click').on('click', function() {
        const id = parseInt($(this).data('id'));
        removeItem(id);
    });
}

function removeItem(id) {
    showConfirm('Apakah Anda yakin ingin menghapus item ini?', function() {
        const itemIndex = transaksiItems.findIndex(item => item.id === id);

        if (itemIndex !== -1) {
            transaksiItems.splice(itemIndex, 1);
            $(`#item_${id}`).remove();

            // Renumber rows
            $('#transaksi_items tr').each(function(index) {
                if ($(this).attr('id') !== 'empty_row') {
                    $(this).find('td:first').text(index + 1);
                }
            });

            // Show empty row if no items
            if (transaksiItems.length === 0) {
                $('#transaksi_items').html(`
                    <tr id="empty_row">
                        <td colspan="8" class="text-center text-muted">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                            Belum ada item. Scan barcode untuk menambah barang.
                        </td>
                    </tr>
                `);
            }

            updateTotals();
            showSuccess('Item berhasil dihapus.');
        }
    });
}

function updateTotals() {
    let totalItem = transaksiItems.length;
    let totalQty = 0;
    let subtotal = 0;
    let totalDiskon = 0;

    transaksiItems.forEach(item => {
        totalQty += item.jumlah;
        const itemSubtotal = item.harga_jual * item.jumlah;
        const itemDiskon = itemSubtotal * item.disc / 100;

        subtotal += itemSubtotal;
        totalDiskon += itemDiskon;
    });

    const grandTotal = subtotal - totalDiskon;

    $('#total_item').text(totalItem);
    $('#total_qty').text(totalQty);
    $('#subtotal').text(formatRupiah(subtotal));
    $('#total_diskon').text(formatRupiah(totalDiskon));
    $('#grand_total').text(formatRupiah(grandTotal));

    // Calculate kembalian
    calculateKembalian();
}

function calculateKembalian() {
    const grandTotal = parseNumber($('#grand_total').text().replace('Rp ', '')) || 0;
    const dibayar = parseNumber($('#dibayar').val()) || 0;
    const kembalian = dibayar - grandTotal;

    $('#kembalian').text(formatRupiah(Math.max(0, kembalian)));

    // Color coding for kembalian
    if (kembalian < 0) {
        $('#kembalian').removeClass('text-success').addClass('text-danger');
    } else {
        $('#kembalian').removeClass('text-danger').addClass('text-success');
    }
}

function showModalCariBarang(searchTerm = '') {
    $('#search_barang').val(searchTerm);
    $('#modalCariBarang').modal('show');

    // Load barang list when modal is shown
    $('#modalCariBarang').on('shown.bs.modal', function() {
        loadBarangModal();
    });
}

function loadBarangModal() {
    const search = $('#search_barang').val().trim();

    ajaxRequest(
        'ajax/penjualan_ajax.php?action=get_barang_list&search=' + encodeURIComponent(search),
        {},
        function(response) {
            let html = '';

            if (response.data && response.data.length > 0) {
                response.data.forEach(barang => {
                    html += `
                        <tr>
                            <td>${barang.barcode}</td>
                            <td>${barang.nama_barang}</td>
                            <td><span class="badge bg-info">${barang.stock} ${barang.nama_satuan}</span></td>
                            <td>${formatRupiah(barang.harga_jual)}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary btn-pilih-barang"
                                        data-barang='${JSON.stringify(barang)}'>
                                    <i class="fas fa-plus"></i> Pilih
                                </button>
                            </td>
                        </tr>
                    `;
                });
            } else {
                html = `
                    <tr>
                        <td colspan="5" class="text-center text-muted">
                            Tidak ada barang ditemukan.
                        </td>
                    </tr>
                `;
            }

            $('#tableBarangModal tbody').html(html);

            // Bind pilih barang events
            $('.btn-pilih-barang').click(function() {
                const barang = JSON.parse($(this).attr('data-barang'));
                const jumlah = parseInt($('#jumlah_input').val()) || 1;

                addItemToTransaction(barang, jumlah);
                $('#modalCariBarang').modal('hide');
                $('#barcode_input').focus();
            });
        },
        function(message) {
            showError(message);
        }
    );
}

function showModalBarcodeScanner() {
    $('#modalBarcodeScanner').modal('show');

    // Initialize barcode scanner (placeholder for now)
    // This would integrate with a barcode scanning library like QuaggaJS
    showError('Fitur barcode scanner akan diimplementasikan pada update selanjutnya.');
}

function simpanTransaksi() {
    // Validation
    if (transaksiItems.length === 0) {
        showError('Tidak ada item untuk disimpan.');
        return;
    }

    const caraBayar = $('#cara_bayar').val();
    if (!caraBayar) {
        showError('Cara bayar harus dipilih.');
        return;
    }

    const dibayar = parseNumber($('#dibayar').val()) || 0;
    const grandTotal = parseNumber($('#grand_total').text().replace('Rp ', '')) || 0;

    if (dibayar < grandTotal) {
        showError('Jumlah dibayar kurang dari total transaksi.');
        return;
    }

    // Prepare data
    const formData = {
        action: 'simpan_transaksi',
        no_transaksi: $('#no_transaksi').val(),
        tgl_penjualan: $('#tgl_penjualan').val(),
        kd_pelanggan: $('#kd_pelanggan').val() || 0,
        cara_bayar: caraBayar,
        dibayar: dibayar,
        kembalian: parseNumber($('#kembalian').text().replace('Rp ', '')) || 0,
        keterangan: $('#keterangan').val(),
        items: JSON.stringify(transaksiItems.map(item => ({
            kd_barang: item.kd_barang,
            harga_beli: item.harga_beli,
            harga_jual: item.harga_jual,
            jumlah: item.jumlah,
            disc: item.disc
        })))
    };

    // Show loading
    const btnSimpan = $('#btnSimpanTransaksi');
    const originalText = btnSimpan.html();
    showLoading(btnSimpan);

    ajaxRequest(
        'ajax/penjualan_ajax.php',
        formData,
        function(response) {
            // Success callback
            hideLoading(btnSimpan, originalText);

            Swal.fire({
                title: 'Transaksi Berhasil!',
                text: `No. Transaksi: ${response.no_transaksi}`,
                icon: 'success',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-print"></i> Print Struk',
                cancelButtonText: 'Transaksi Baru',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed) {
                    printStruk(response.kd_penjualan);
                }
                resetTransaksi();
                generateNewTransactionNumber();
            });
        },
        function(message) {
            // Error callback
            hideLoading(btnSimpan, originalText);
            showError(message);
        }
    );
}

function resetTransaksi() {
    // Confirm reset if there are items
    if (transaksiItems.length > 0) {
        showConfirm('Apakah Anda yakin ingin mereset transaksi?', function() {
            performReset();
        });
    } else {
        performReset();
    }
}

function performReset() {
    // Clear items
    transaksiItems = [];
    itemCounter = 0;

    // Reset form
    $('#formTransaksi')[0].reset();
    $('#tgl_penjualan').val(new Date().toISOString().split('T')[0]);

    // Reset inputs
    $('#barcode_input').val('');
    $('#jumlah_input').val(1);
    $('#dibayar').val('');

    // Reset table
    $('#transaksi_items').html(`
        <tr id="empty_row">
            <td colspan="8" class="text-center text-muted">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                Belum ada item. Scan barcode untuk menambah barang.
            </td>
        </tr>
    `);

    // Reset totals
    updateTotals();

    // Focus on barcode input
    $('#barcode_input').focus();

    showSuccess('Transaksi berhasil direset.');
}

function generateNewTransactionNumber() {
    // Generate new transaction number
    const now = new Date();
    const dateStr = now.getFullYear().toString() +
                   (now.getMonth() + 1).toString().padStart(2, '0') +
                   now.getDate().toString().padStart(2, '0');
    const timeStr = now.getHours().toString().padStart(2, '0') +
                   now.getMinutes().toString().padStart(2, '0') +
                   now.getSeconds().toString().padStart(2, '0');

    const newTransactionNumber = 'PJ' + dateStr + timeStr;
    $('#no_transaksi').val(newTransactionNumber);
}

function printStruk(kd_penjualan) {
    // Open print window
    const printWindow = window.open(`print_struk.php?id=${kd_penjualan}`, '_blank', 'width=300,height=600');

    if (printWindow) {
        printWindow.onload = function() {
            printWindow.print();
        };
    } else {
        showError('Popup diblokir. Silakan izinkan popup untuk print struk.');
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // F1 - Focus barcode input
    if (e.which === 112) {
        e.preventDefault();
        $('#barcode_input').focus();
    }

    // F2 - Open search modal
    if (e.which === 113) {
        e.preventDefault();
        showModalCariBarang();
    }

    // F9 - Save transaction
    if (e.which === 120) {
        e.preventDefault();
        simpanTransaksi();
    }

    // F12 - Reset transaction
    if (e.which === 123) {
        e.preventDefault();
        resetTransaksi();
    }
});

// Auto-save draft (optional feature)
setInterval(function() {
    if (transaksiItems.length > 0) {
        localStorage.setItem('penjualan_draft', JSON.stringify({
            items: transaksiItems,
            form_data: {
                kd_pelanggan: $('#kd_pelanggan').val(),
                cara_bayar: $('#cara_bayar').val(),
                keterangan: $('#keterangan').val()
            }
        }));
    }
}, 30000); // Save every 30 seconds

// Load draft on page load
$(window).on('load', function() {
    const draft = localStorage.getItem('penjualan_draft');
    if (draft) {
        try {
            const draftData = JSON.parse(draft);

            if (draftData.items && draftData.items.length > 0) {
                Swal.fire({
                    title: 'Draft Ditemukan',
                    text: 'Ditemukan draft transaksi sebelumnya. Muat draft?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Muat Draft',
                    cancelButtonText: 'Tidak'
                }).then((result) => {
                    if (result.isConfirmed) {
                        loadDraft(draftData);
                    } else {
                        localStorage.removeItem('penjualan_draft');
                    }
                });
            }
        } catch (e) {
            localStorage.removeItem('penjualan_draft');
        }
    }
});

function loadDraft(draftData) {
    // Load items
    transaksiItems = draftData.items || [];
    itemCounter = Math.max(...transaksiItems.map(item => item.id)) || 0;

    // Load form data
    if (draftData.form_data) {
        $('#kd_pelanggan').val(draftData.form_data.kd_pelanggan);
        $('#cara_bayar').val(draftData.form_data.cara_bayar);
        $('#keterangan').val(draftData.form_data.keterangan);
    }

    // Rebuild table
    $('#transaksi_items').empty();
    transaksiItems.forEach(item => {
        addItemRow(item);
    });

    updateTotals();
    bindItemEvents();

    showSuccess('Draft berhasil dimuat.');
    localStorage.removeItem('penjualan_draft');
}
