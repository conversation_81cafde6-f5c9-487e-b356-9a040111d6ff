/**
 * Satuan Management JavaScript
 */

$(document).ready(function() {
    // Initialize DataTable
    var table = initDataTable('#tableSatuan', {
        ajax: {
            url: 'satuan/ajax/satuan_ajax.php',
            type: 'GET'
        },
        columns: [
            { data: 0, orderable: false },
            { data: 1 },
            { data: 2, orderable: false, searchable: false }
        ],
        order: [[1, 'asc']]
    });
    
    // Handle form submission
    $('#formSatuan').submit(function(e) {
        e.preventDefault();
        
        if (!validateForm('#formSatuan')) {
            return false;
        }
        
        var formData = $(this).serialize();
        var btnSimpan = $('#btnSimpan');
        var originalText = btnSimpan.html();
        
        // Show loading
        showLoading(btnSimpan);
        
        ajaxRequest(
            'satuan/ajax/satuan_ajax.php',
            formData,
            function(response) {
                // Success callback
                hideLoading(btnSimpan, originalText);
                showSuccess(response.message);
                $('#modalSatuan').modal('hide');
                table.ajax.reload();
                resetForm('#formSatuan');
            },
            function(message) {
                // Error callback
                hideLoading(btnSimpan, originalText);
                showError(message);
            }
        );
    });
    
    // Handle edit button click
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        
        // Reset form
        resetForm('#formSatuan');
        
        // Change modal title and action
        $('#modalSatuanLabel').html('<i class="fas fa-edit me-2"></i>Edit Satuan');
        $('#action').val('edit');
        
        // Get satuan data
        ajaxRequest(
            'satuan/ajax/satuan_ajax.php',
            { action: 'get', kd_satuan: id },
            function(response) {
                // Fill form with data
                $('#kd_satuan').val(response.data.kd_satuan);
                $('#nama_satuan').val(response.data.nama_satuan);
                
                // Show modal
                $('#modalSatuan').modal('show');
            },
            function(message) {
                showError(message);
            }
        );
    });
    
    // Reset modal when closed
    $('#modalSatuan').on('hidden.bs.modal', function() {
        resetForm('#formSatuan');
        $('#modalSatuanLabel').html('<i class="fas fa-plus me-2"></i>Tambah Satuan');
        $('#action').val('add');
        $('#kd_satuan').val('');
    });
    
    // Handle form validation
    $('#nama_satuan').on('input', function() {
        var value = $(this).val().trim();
        if (value.length > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Auto-focus on modal show
    $('#modalSatuan').on('shown.bs.modal', function() {
        $('#nama_satuan').focus();
    });
});
