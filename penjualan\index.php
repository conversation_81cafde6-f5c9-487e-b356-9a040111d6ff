<?php
$page_title = 'Transaksi Penjualan';
require_once '../config/database.php';
require_once '../includes/header.php';

// Get master data for dropdowns
$connection = getConnection();

// Get pelanggan
$pelanggan_query = "SELECT kd_pelanggan, nama_pelanggan FROM pelanggan ORDER BY nama_pelanggan ASC";
$pelanggan_result = $connection->query($pelanggan_query);

// Get cara bayar
$pembayaran_query = "SELECT id_pembayaran, cara_bayar FROM pembayaran ORDER BY cara_bayar ASC";
$pembayaran_result = $connection->query($pembayaran_query);

closeConnection($connection);

// Generate transaction number
$no_transaksi = generatePenjualanTransactionNumber();
?>

<div class="row">
    <div class="col-12">
        <div class="mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-shopping-cart me-2"></i>Transaksi Penjualan
            </h1>
        </div>
    </div>
</div>

<div class="row">
    <!-- Left Panel - Transaction Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-barcode me-2"></i>Input Barang
                </h5>
            </div>
            <div class="card-body">
                <!-- Barcode Scanner Section -->
                <div class="row mb-3">
                    <div class="col-12">
                        <label for="barcode_input" class="form-label">Scan/Input Barcode</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-lg" id="barcode_input" placeholder="Scan barcode atau ketik nama barang" autofocus>
                            <button class="btn btn-primary" type="button" id="btnCariBarang">
                                <i class="fas fa-search"></i> Cari
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Barang akan ditambah dengan qty 1. Edit qty di kolom "Qty" jika diperlukan.
                        </div>
                    </div>
                </div>

                <!-- Transaction Items Table -->
                <div class="table-responsive">
                    <table class="table table-striped" id="tableTransaksi">
                        <thead class="table-dark">
                            <tr>
                                <th width="5%">No</th>
                                <th width="15%">Barcode</th>
                                <th width="25%">Nama Barang</th>
                                <th width="10%">Harga</th>
                                <th width="8%">Qty</th>
                                <th width="10%">Disc (%)</th>
                                <th width="15%">Subtotal</th>
                                <th width="12%">Aksi</th>
                            </tr>
                        </thead>
                        <tbody id="transaksi_items">
                            <tr id="empty_row">
                                <td colspan="8" class="text-center text-muted">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                                    Belum ada item. Scan barcode untuk menambah barang.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Panel - Transaction Summary -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>Detail Transaksi
                </h5>
            </div>
            <div class="card-body">
                <form id="formTransaksi">
                    <div class="mb-3">
                        <label for="no_transaksi" class="form-label">No. Transaksi</label>
                        <input type="text" class="form-control" id="no_transaksi" name="no_transaksi" value="<?php echo $no_transaksi; ?>" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="tgl_penjualan" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tgl_penjualan" name="tgl_penjualan" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>

                    <div class="mb-3" id="div_tgl_jt" style="display: none;">
                        <label for="tgl_jt" class="form-label">Tanggal Jatuh Tempo</label>
                        <input type="date" class="form-control" id="tgl_jt" name="tgl_jt" value="<?php echo date('Y-m-d'); ?>">
                        <div class="form-text">
                            <i class="fas fa-calendar-alt me-1"></i>
                            Tentukan kapan pembayaran kredit jatuh tempo
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="kd_pelanggan" class="form-label">Pelanggan</label>
                        <select class="form-select" id="kd_pelanggan" name="kd_pelanggan">
                            <?php
                            // Reset pointer untuk pelanggan
                            $pelanggan_result->data_seek(0);
                            while ($pelanggan = $pelanggan_result->fetch_assoc()):
                                $selected = ($pelanggan['kd_pelanggan'] == 1) ? 'selected' : '';
                            ?>
                                <option value="<?php echo $pelanggan['kd_pelanggan']; ?>" <?php echo $selected; ?>>
                                    <?php echo htmlspecialchars($pelanggan['nama_pelanggan']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="cara_bayar" class="form-label">Cara Bayar</label>
                        <select class="form-select" id="cara_bayar" name="cara_bayar" required>
                            <?php
                            // Reset pointer untuk pembayaran
                            $pembayaran_result->data_seek(0);
                            while ($pembayaran = $pembayaran_result->fetch_assoc()):
                                $selected = ($pembayaran['cara_bayar'] == 'Cash') ? 'selected' : '';
                            ?>
                                <option value="<?php echo htmlspecialchars($pembayaran['cara_bayar']); ?>" <?php echo $selected; ?>>
                                    <?php echo htmlspecialchars($pembayaran['cara_bayar']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <hr>

                    <!-- Transaction Summary -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Item:</span>
                            <span id="total_item">0</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Qty:</span>
                            <span id="total_qty">0</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Subtotal:</span>
                            <span id="subtotal">Rp 0</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Diskon:</span>
                            <span id="total_diskon">Rp 0</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between fw-bold fs-5">
                            <span>TOTAL:</span>
                            <span id="grand_total" class="text-primary">Rp 0</span>
                        </div>
                    </div>

                    <hr>

                    <!-- Payment Section -->
                    <div class="mb-3">
                        <label for="dibayar" class="form-label">Dibayar</label>
                        <input type="text" class="form-control currency-input" id="dibayar" name="dibayar" placeholder="0">
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Kembalian:</span>
                            <span id="kembalian" class="text-success">Rp 0</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="2" placeholder="Keterangan tambahan (opsional)"></textarea>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mb-3">
                        <button type="button" class="btn btn-success btn-lg me-2" id="btnSimpanTransaksi">
                            <i class="fas fa-save me-2"></i>Simpan Transaksi
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg" id="btnResetTransaksi">
                            <i class="fas fa-refresh me-2"></i>Reset
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Cari Barang -->
<div class="modal fade" id="modalCariBarang" tabindex="-1" aria-labelledby="modalCariBarangLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalCariBarangLabel">
                    <i class="fas fa-search me-2"></i>Cari Barang
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="search_barang" placeholder="Ketik nama barang atau barcode...">
                </div>
                <div class="table-responsive">
                    <table id="tableBarangModal" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Barcode</th>
                                <th>Nama Barang</th>
                                <th>Stock</th>
                                <th>Harga</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal removed - not needed -->

<?php
$page_scripts = ['penjualan/js/penjualan.js'];
require_once '../includes/footer.php';
?>
