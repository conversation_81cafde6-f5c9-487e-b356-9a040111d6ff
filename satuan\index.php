<?php
$page_title = 'Master Satuan';
require_once '../config/database.php';
require_once '../includes/header.php';

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $kd_satuan = (int)$_GET['id'];
    $connection = getConnection();
    
    // Check if satuan is used in barang
    $check_query = "SELECT COUNT(*) as count FROM barang WHERE kd_satuan = ?";
    $check_stmt = $connection->prepare($check_query);
    $check_stmt->bind_param("i", $kd_satuan);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $count = $check_result->fetch_assoc()['count'];
    
    if ($count > 0) {
        $error_message = "Satuan tidak dapat dihapus karena masih digunakan pada $count barang.";
    } else {
        $delete_query = "DELETE FROM satuan WHERE kd_satuan = ?";
        $delete_stmt = $connection->prepare($delete_query);
        $delete_stmt->bind_param("i", $kd_satuan);
        
        if ($delete_stmt->execute()) {
            $success_message = "Satuan berhasil dihapus.";
        } else {
            $error_message = "Gagal menghapus satuan.";
        }
        $delete_stmt->close();
    }
    
    $check_stmt->close();
    closeConnection($connection);
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-balance-scale me-2"></i>Master Satuan
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalSatuan">
                <i class="fas fa-plus me-2"></i>Tambah Satuan
            </button>
        </div>
    </div>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Data Satuan</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="tableSatuan" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="10%">No</th>
                                <th width="70%">Nama Satuan</th>
                                <th width="20%">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via DataTables AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Satuan -->
<div class="modal fade" id="modalSatuan" tabindex="-1" aria-labelledby="modalSatuanLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalSatuanLabel">
                    <i class="fas fa-balance-scale me-2"></i>Tambah Satuan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formSatuan">
                <div class="modal-body">
                    <input type="hidden" id="kd_satuan" name="kd_satuan">
                    <input type="hidden" id="action" name="action" value="add">
                    
                    <div class="mb-3">
                        <label for="nama_satuan" class="form-label">Nama Satuan <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nama_satuan" name="nama_satuan" required maxlength="50" placeholder="Contoh: Pcs, Kg, Liter, Meter">
                        <div class="invalid-feedback">
                            Nama satuan harus diisi.
                        </div>
                        <div class="form-text">
                            Contoh: Pcs, Kg, Liter, Meter, Box, Karton, dll.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="btnSimpan">
                        <i class="fas fa-save me-2"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$page_scripts = ['satuan/js/satuan.js'];
require_once '../includes/footer.php';
?>
