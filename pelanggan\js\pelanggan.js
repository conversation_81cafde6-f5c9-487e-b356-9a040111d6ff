/**
 * Pelanggan Management JavaScript
 */

$(document).ready(function() {
    // Initialize DataTable
    var table = initDataTable('#tablePelanggan', {
        ajax: {
            url: 'ajax/pelanggan_ajax.php',
            type: 'GET'
        },
        columns: [
            { data: 0, orderable: false },
            { data: 1 },
            { data: 2, orderable: false, searchable: false }
        ],
        order: [[1, 'asc']]
    });
    
    // Handle form submission
    $('#formPelanggan').submit(function(e) {
        e.preventDefault();
        
        if (!validateForm('#formPelanggan')) {
            return false;
        }
        
        var formData = $(this).serialize();
        var btnSimpan = $('#btnSimpan');
        var originalText = btnSimpan.html();
        
        // Show loading
        showLoading(btnSimpan);
        
        ajaxRequest(
            'ajax/pelanggan_ajax.php',
            formData,
            function(response) {
                // Success callback
                hideLoading(btnSimpan, originalText);
                showSuccess(response.message);
                $('#modalPelanggan').modal('hide');
                table.ajax.reload();
                resetForm('#formPelanggan');
            },
            function(message) {
                // Error callback
                hideLoading(btnSimpan, originalText);
                showError(message);
            }
        );
    });
    
    // Handle edit button click
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        
        // Reset form
        resetForm('#formPelanggan');
        
        // Change modal title and action
        $('#modalPelangganLabel').html('<i class="fas fa-edit me-2"></i>Edit Pelanggan');
        $('#action').val('edit');
        
        // Get pelanggan data
        ajaxRequest(
            'ajax/pelanggan_ajax.php',
            { action: 'get', kd_pelanggan: id },
            function(response) {
                // Fill form with data
                $('#kd_pelanggan').val(response.data.kd_pelanggan);
                $('#nama_pelanggan').val(response.data.nama_pelanggan);
                
                // Show modal
                $('#modalPelanggan').modal('show');
            },
            function(message) {
                showError(message);
            }
        );
    });
    
    // Reset modal when closed
    $('#modalPelanggan').on('hidden.bs.modal', function() {
        resetForm('#formPelanggan');
        $('#modalPelangganLabel').html('<i class="fas fa-plus me-2"></i>Tambah Pelanggan');
        $('#action').val('add');
        $('#kd_pelanggan').val('');
    });
    
    // Handle form validation
    $('#nama_pelanggan').on('input', function() {
        var value = $(this).val().trim();
        if (value.length > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Auto-focus on modal show
    $('#modalPelanggan').on('shown.bs.modal', function() {
        $('#nama_pelanggan').focus();
    });
});
