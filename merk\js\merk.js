/**
 * Merk Management JavaScript
 */

$(document).ready(function() {
    // Initialize DataTable
    var table = initDataTable('#tableMerk', {
        ajax: {
            url: 'merk/ajax/merk_ajax.php',
            type: 'GET'
        },
        columns: [
            { data: 0, orderable: false },
            { data: 1 },
            { data: 2, orderable: false, searchable: false }
        ],
        order: [[1, 'asc']]
    });
    
    // Handle form submission
    $('#formMerk').submit(function(e) {
        e.preventDefault();
        
        if (!validateForm('#formMerk')) {
            return false;
        }
        
        var formData = $(this).serialize();
        var btnSimpan = $('#btnSimpan');
        var originalText = btnSimpan.html();
        
        // Show loading
        showLoading(btnSimpan);
        
        ajaxRequest(
            'merk/ajax/merk_ajax.php',
            formData,
            function(response) {
                // Success callback
                hideLoading(btnSimpan, originalText);
                showSuccess(response.message);
                $('#modalMerk').modal('hide');
                table.ajax.reload();
                resetForm('#formMerk');
            },
            function(message) {
                // Error callback
                hideLoading(btnSimpan, originalText);
                showError(message);
            }
        );
    });
    
    // Handle edit button click
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        
        // Reset form
        resetForm('#formMerk');
        
        // Change modal title and action
        $('#modalMerkLabel').html('<i class="fas fa-edit me-2"></i>Edit Merk');
        $('#action').val('edit');
        
        // Get merk data
        ajaxRequest(
            'merk/ajax/merk_ajax.php',
            { action: 'get', kd_merk: id },
            function(response) {
                // Fill form with data
                $('#kd_merk').val(response.data.kd_merk);
                $('#nama_merk').val(response.data.nama_merk);
                
                // Show modal
                $('#modalMerk').modal('show');
            },
            function(message) {
                showError(message);
            }
        );
    });
    
    // Reset modal when closed
    $('#modalMerk').on('hidden.bs.modal', function() {
        resetForm('#formMerk');
        $('#modalMerkLabel').html('<i class="fas fa-plus me-2"></i>Tambah Merk');
        $('#action').val('add');
        $('#kd_merk').val('');
    });
    
    // Handle form validation
    $('#nama_merk').on('input', function() {
        var value = $(this).val().trim();
        if (value.length > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Auto-focus on modal show
    $('#modalMerk').on('shown.bs.modal', function() {
        $('#nama_merk').focus();
    });
});
