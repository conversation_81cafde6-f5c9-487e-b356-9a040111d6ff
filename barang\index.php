<?php
$page_title = 'Master Barang';
require_once '../config/database.php';
require_once '../includes/header.php';

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $kd_barang = (int)$_GET['id'];
    $connection = getConnection();
    
    // Check if barang is used in transactions
    $check_query = "SELECT 
                        (SELECT COUNT(*) FROM pembelian_item WHERE kd_barang = ?) +
                        (SELECT COUNT(*) FROM penjualan_item WHERE kd_barang = ?) +
                        (SELECT COUNT(*) FROM retur_pembelian_item WHERE kd_barang = ?) as count";
    $check_stmt = $connection->prepare($check_query);
    $check_stmt->bind_param("iii", $kd_barang, $kd_barang, $kd_barang);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $count = $check_result->fetch_assoc()['count'];
    
    if ($count > 0) {
        $error_message = "Barang tidak dapat dihapus karena masih digunakan pada $count transaksi.";
    } else {
        $delete_query = "DELETE FROM barang WHERE kd_barang = ?";
        $delete_stmt = $connection->prepare($delete_query);
        $delete_stmt->bind_param("i", $kd_barang);
        
        if ($delete_stmt->execute()) {
            $success_message = "Barang berhasil dihapus.";
        } else {
            $error_message = "Gagal menghapus barang.";
        }
        $delete_stmt->close();
    }
    
    $check_stmt->close();
    closeConnection($connection);
}

// Get master data for dropdowns
$connection = getConnection();

// Get kategori
$kategori_query = "SELECT kd_kategori, nama_kategori FROM kategori ORDER BY nama_kategori ASC";
$kategori_result = $connection->query($kategori_query);

// Get merk
$merk_query = "SELECT kd_merk, nama_merk FROM merk ORDER BY nama_merk ASC";
$merk_result = $connection->query($merk_query);

// Get satuan
$satuan_query = "SELECT kd_satuan, nama_satuan FROM satuan ORDER BY nama_satuan ASC";
$satuan_result = $connection->query($satuan_query);

closeConnection($connection);
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-box me-2"></i>Master Barang
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalBarang">
                <i class="fas fa-plus me-2"></i>Tambah Barang
            </button>
        </div>
    </div>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Data Barang</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="tableBarang" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">No</th>
                                <th width="12%">Barcode</th>
                                <th width="20%">Nama Barang</th>
                                <th width="10%">Kategori</th>
                                <th width="10%">Merk</th>
                                <th width="8%">Satuan</th>
                                <th width="8%">Stock</th>
                                <th width="12%">Harga Jual</th>
                                <th width="15%">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via DataTables AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Barang -->
<div class="modal fade" id="modalBarang" tabindex="-1" aria-labelledby="modalBarangLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalBarangLabel">
                    <i class="fas fa-box me-2"></i>Tambah Barang
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formBarang">
                <div class="modal-body">
                    <input type="hidden" id="kd_barang" name="kd_barang">
                    <input type="hidden" id="action" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="barcode" class="form-label">Barcode <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="barcode" name="barcode" required maxlength="50" placeholder="Scan atau ketik barcode">
                                    <button class="btn btn-outline-secondary" type="button" id="generateBarcode" title="Generate Barcode Otomatis">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Barcode harus diisi.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nama_barang" class="form-label">Nama Barang <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nama_barang" name="nama_barang" required maxlength="50" placeholder="Nama barang">
                                <div class="invalid-feedback">
                                    Nama barang harus diisi.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="kd_kategori" class="form-label">Kategori <span class="text-danger">*</span></label>
                                <select class="form-select" id="kd_kategori" name="kd_kategori" required>
                                    <option value="">Pilih Kategori</option>
                                    <?php while ($kategori = $kategori_result->fetch_assoc()): ?>
                                        <option value="<?php echo $kategori['kd_kategori']; ?>">
                                            <?php echo htmlspecialchars($kategori['nama_kategori']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Kategori harus dipilih.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="kd_merk" class="form-label">Merk <span class="text-danger">*</span></label>
                                <select class="form-select" id="kd_merk" name="kd_merk" required>
                                    <option value="">Pilih Merk</option>
                                    <?php while ($merk = $merk_result->fetch_assoc()): ?>
                                        <option value="<?php echo $merk['kd_merk']; ?>">
                                            <?php echo htmlspecialchars($merk['nama_merk']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Merk harus dipilih.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="kd_satuan" class="form-label">Satuan <span class="text-danger">*</span></label>
                                <select class="form-select" id="kd_satuan" name="kd_satuan" required>
                                    <option value="">Pilih Satuan</option>
                                    <?php while ($satuan = $satuan_result->fetch_assoc()): ?>
                                        <option value="<?php echo $satuan['kd_satuan']; ?>">
                                            <?php echo htmlspecialchars($satuan['nama_satuan']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Satuan harus dipilih.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="stock" class="form-label">Stock <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="stock" name="stock" required min="0" placeholder="0">
                                <div class="invalid-feedback">
                                    Stock harus diisi.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="harga_beli" class="form-label">Harga Beli <span class="text-danger">*</span></label>
                                <input type="text" class="form-control currency-input" id="harga_beli" name="harga_beli" required placeholder="0">
                                <div class="invalid-feedback">
                                    Harga beli harus diisi.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="harga_jual" class="form-label">Harga Jual <span class="text-danger">*</span></label>
                                <input type="text" class="form-control currency-input" id="harga_jual" name="harga_jual" required placeholder="0">
                                <div class="invalid-feedback">
                                    Harga jual harus diisi.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="margin" class="form-label">Margin</label>
                                <input type="text" class="form-control" id="margin" readonly placeholder="0%">
                                <div class="form-text">
                                    Margin keuntungan otomatis terhitung.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="3" placeholder="Keterangan tambahan tentang barang (opsional)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="btnSimpan">
                        <i class="fas fa-save me-2"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$page_scripts = ['barang/js/barang.js'];
require_once '../includes/footer.php';
?>
