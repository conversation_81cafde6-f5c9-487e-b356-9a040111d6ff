<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is logged in
requireLogin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            $nama_kategori = trim($_POST['nama_kategori'] ?? '');
            
            if (empty($nama_kategori)) {
                $response['message'] = 'Nama kategori harus diisi.';
                break;
            }
            
            // Check if kategori already exists
            $check_query = "SELECT kd_kategori FROM kategori WHERE nama_kategori = ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("s", $nama_kategori);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama kategori sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Insert new kategori
            $insert_query = "INSERT INTO kategori (nama_kategori) VALUES (?)";
            $insert_stmt = $connection->prepare($insert_query);
            $insert_stmt->bind_param("s", $nama_kategori);
            
            if ($insert_stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'Kategori berhasil ditambahkan.';
            } else {
                $response['message'] = 'Gagal menambahkan kategori.';
            }
            $insert_stmt->close();
            break;
            
        case 'edit':
            $kd_kategori = (int)($_POST['kd_kategori'] ?? 0);
            $nama_kategori = trim($_POST['nama_kategori'] ?? '');
            
            if (empty($nama_kategori)) {
                $response['message'] = 'Nama kategori harus diisi.';
                break;
            }
            
            if ($kd_kategori <= 0) {
                $response['message'] = 'ID kategori tidak valid.';
                break;
            }
            
            // Check if kategori name already exists (except current record)
            $check_query = "SELECT kd_kategori FROM kategori WHERE nama_kategori = ? AND kd_kategori != ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("si", $nama_kategori, $kd_kategori);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama kategori sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Update kategori
            $update_query = "UPDATE kategori SET nama_kategori = ? WHERE kd_kategori = ?";
            $update_stmt = $connection->prepare($update_query);
            $update_stmt->bind_param("si", $nama_kategori, $kd_kategori);
            
            if ($update_stmt->execute()) {
                if ($update_stmt->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Kategori berhasil diperbarui.';
                } else {
                    $response['message'] = 'Tidak ada perubahan data.';
                }
            } else {
                $response['message'] = 'Gagal memperbarui kategori.';
            }
            $update_stmt->close();
            break;
            
        case 'get':
            $kd_kategori = (int)($_POST['kd_kategori'] ?? 0);
            
            if ($kd_kategori <= 0) {
                $response['message'] = 'ID kategori tidak valid.';
                break;
            }
            
            $query = "SELECT * FROM kategori WHERE kd_kategori = ?";
            $stmt = $connection->prepare($query);
            $stmt->bind_param("i", $kd_kategori);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $response['success'] = true;
                $response['data'] = $result->fetch_assoc();
            } else {
                $response['message'] = 'Kategori tidak ditemukan.';
            }
            $stmt->close();
            break;
            
        default:
            $response['message'] = 'Aksi tidak valid.';
            break;
    }
}

// Handle DataTables server-side processing
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['draw'])) {
    $draw = (int)$_GET['draw'];
    $start = (int)$_GET['start'];
    $length = (int)$_GET['length'];
    $search = $_GET['search']['value'] ?? '';
    
    // Base query
    $base_query = "FROM kategori";
    $where_clause = "";
    $params = [];
    $param_types = "";
    
    // Search functionality
    if (!empty($search)) {
        $where_clause = " WHERE nama_kategori LIKE ?";
        $params[] = "%$search%";
        $param_types .= "s";
    }
    
    // Count total records
    $count_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    $count_stmt = $connection->prepare($count_query);
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
    
    // Get data with pagination
    $data_query = "SELECT kd_kategori, nama_kategori " . $base_query . $where_clause . " ORDER BY nama_kategori ASC LIMIT ?, ?";
    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";
    
    $data_stmt = $connection->prepare($data_query);
    $data_stmt->bind_param($param_types, ...$params);
    $data_stmt->execute();
    $result = $data_stmt->get_result();
    
    $data = [];
    $no = $start + 1;
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            $no++,
            htmlspecialchars($row['nama_kategori']),
            '<div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-warning btn-edit" data-id="' . $row['kd_kategori'] . '" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <a href="?action=delete&id=' . $row['kd_kategori'] . '" class="btn btn-sm btn-danger btn-delete" title="Hapus" data-message="Apakah Anda yakin ingin menghapus kategori ' . htmlspecialchars($row['nama_kategori']) . '?">
                    <i class="fas fa-trash"></i>
                </a>
            </div>'
        ];
    }
    $data_stmt->close();
    
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ];
}

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
?>
