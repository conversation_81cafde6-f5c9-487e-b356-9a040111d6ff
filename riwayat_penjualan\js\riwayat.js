$(document).ready(function() {
    // Initialize page
    initializeRiwayat();
    
    // Load summary data
    loadSummaryData();
    
    // Initialize DataTable
    initializeDataTable();
    
    // Event handlers
    $('#btnFilter').click(function() {
        filterTransactions();
    });
    
    $('#btnReset').click(function() {
        resetFilters();
    });
    
    $('#btnExport').click(function() {
        exportData();
    });
    
    // Auto filter on enter
    $('#filter_no_transaksi').on('keypress', function(e) {
        if (e.which === 13) {
            filterTransactions();
        }
    });
});

function initializeRiwayat() {
    // Initialize Select2 for filters
    $('#filter_pelanggan, #filter_kasir, #filter_cara_bayar').select2({
        theme: 'bootstrap-5',
        allowClear: true,
        width: '100%'
    });
}

function loadSummaryData() {
    ajaxRequest(
        'ajax/riwayat_ajax.php',
        { action: 'get_summary' },
        function(response) {
            if (response.success && response.data) {
                const data = response.data;
                
                // Update summary cards
                $('#summary_today').html(
                    `<small>${data.today.count} transaksi</small><br>` +
                    `<strong>${formatRupiah(data.today.total)}</strong>`
                );
                
                $('#summary_week').html(
                    `<small>${data.week.count} transaksi</small><br>` +
                    `<strong>${formatRupiah(data.week.total)}</strong>`
                );
                
                $('#summary_month').html(
                    `<small>${data.month.count} transaksi</small><br>` +
                    `<strong>${formatRupiah(data.month.total)}</strong>`
                );
                
                $('#summary_total').html(
                    `<small>${data.total.count} transaksi</small><br>` +
                    `<strong>${formatRupiah(data.total.total)}</strong>`
                );
            }
        },
        function(message) {
            console.error('Error loading summary:', message);
        }
    );
}

let dataTable;

function initializeDataTable() {
    dataTable = $('#tableRiwayat').DataTable({
        processing: true,
        serverSide: false,
        responsive: true,
        pageLength: 25,
        order: [[1, 'desc']], // Sort by date descending
        columnDefs: [
            {
                targets: [4], // Total column
                render: function(data, type, row) {
                    return formatRupiah(data);
                }
            },
            {
                targets: [7], // Status column
                render: function(data, type, row) {
                    return `<span class="badge bg-${row.status_class}">${data}</span>`;
                }
            },
            {
                targets: [8], // Action column
                orderable: false,
                render: function(data, type, row) {
                    return `
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                    onclick="showDetailTransaksi(${row.kd_penjualan})" 
                                    title="Lihat Detail">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" 
                                    onclick="printStruk(${row.kd_penjualan})" 
                                    title="Print Struk">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(difilter dari _MAX_ total data)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            },
            emptyTable: "Tidak ada data yang tersedia"
        }
    });
    
    // Load initial data
    filterTransactions();
}

function filterTransactions() {
    const filterData = {
        action: 'get_transactions',
        tanggal_dari: $('#filter_tanggal_dari').val(),
        tanggal_sampai: $('#filter_tanggal_sampai').val(),
        pelanggan: $('#filter_pelanggan').val(),
        kasir: $('#filter_kasir').val(),
        cara_bayar: $('#filter_cara_bayar').val(),
        no_transaksi: $('#filter_no_transaksi').val(),
        status: $('#filter_status').val()
    };
    
    ajaxRequest(
        'ajax/riwayat_ajax.php',
        filterData,
        function(response) {
            if (response.success && response.data) {
                // Clear and reload DataTable
                dataTable.clear();
                
                response.data.forEach(function(row) {
                    dataTable.row.add([
                        row.no_transaksi,
                        formatDate(row.tgl_penjualan),
                        row.jam,
                        row.nama_pelanggan,
                        row.total_transaksi,
                        row.cara_bayar,
                        row.nama_user,
                        row.status_label,
                        '' // Action column will be rendered by columnDefs
                    ]);
                    
                    // Store additional data for actions
                    const lastRow = dataTable.row(':last').node();
                    $(lastRow).data('kd_penjualan', row.kd_penjualan);
                    $(lastRow).data('status_class', row.status_class);
                });
                
                dataTable.draw();
            } else {
                showError(response.message || 'Gagal memuat data transaksi');
            }
        },
        function(message) {
            showError('Terjadi kesalahan: ' + message);
        }
    );
}

function resetFilters() {
    $('#filterForm')[0].reset();
    $('#filter_tanggal_dari').val(new Date().toISOString().split('T')[0].slice(0, 8) + '01'); // First day of month
    $('#filter_tanggal_sampai').val(new Date().toISOString().split('T')[0]); // Today
    $('#filter_pelanggan, #filter_kasir, #filter_cara_bayar').val(null).trigger('change');
    filterTransactions();
}

function showDetailTransaksi(kd_penjualan) {
    ajaxRequest(
        'ajax/riwayat_ajax.php',
        { 
            action: 'get_detail',
            kd_penjualan: kd_penjualan
        },
        function(response) {
            if (response.success && response.data) {
                const data = response.data;
                const header = data.header;
                const items = data.items;
                
                let itemsHtml = '';
                let subtotal = 0;
                let totalDiskon = 0;
                
                items.forEach(function(item) {
                    const itemSubtotal = item.harga_jual * item.jumlah;
                    const itemDiskon = (itemSubtotal * item.disc) / 100;
                    const itemTotal = itemSubtotal - itemDiskon;
                    
                    subtotal += itemSubtotal;
                    totalDiskon += itemDiskon;
                    
                    itemsHtml += `
                        <tr>
                            <td>${item.barcode}</td>
                            <td>${item.nama_barang}</td>
                            <td class="text-center">${item.jumlah}</td>
                            <td class="text-end">${formatRupiah(item.harga_jual)}</td>
                            <td class="text-center">${item.disc}%</td>
                            <td class="text-end">${formatRupiah(itemTotal)}</td>
                        </tr>
                    `;
                });
                
                const grandTotal = subtotal - totalDiskon;
                
                const modalContent = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Informasi Transaksi</h6>
                            <table class="table table-sm">
                                <tr><td>No. Transaksi:</td><td><strong>${header.no_transaksi}</strong></td></tr>
                                <tr><td>Tanggal:</td><td>${formatDate(header.tgl_penjualan)}</td></tr>
                                <tr><td>Jam:</td><td>${formatTime(header.tgl_penjualan)}</td></tr>
                                <tr><td>Kasir:</td><td>${header.nama_user}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Informasi Pembayaran</h6>
                            <table class="table table-sm">
                                <tr><td>Pelanggan:</td><td>${header.nama_pelanggan || 'Umum'}</td></tr>
                                <tr><td>Cara Bayar:</td><td>${header.cara_bayar}</td></tr>
                                <tr><td>Dibayar:</td><td>${formatRupiah(header.dibayar)}</td></tr>
                                <tr><td>Kembalian:</td><td>${formatRupiah(header.kembalian)}</td></tr>
                            </table>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>Detail Item</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Barcode</th>
                                    <th>Nama Barang</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-end">Harga</th>
                                    <th class="text-center">Diskon</th>
                                    <th class="text-end">Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${itemsHtml}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6 offset-md-6">
                            <table class="table table-sm">
                                <tr><td>Subtotal:</td><td class="text-end">${formatRupiah(subtotal)}</td></tr>
                                <tr><td>Total Diskon:</td><td class="text-end">${formatRupiah(totalDiskon)}</td></tr>
                                <tr class="fw-bold"><td>Grand Total:</td><td class="text-end">${formatRupiah(grandTotal)}</td></tr>
                            </table>
                        </div>
                    </div>
                    
                    ${header.keterangan ? `<div class="mt-3"><strong>Keterangan:</strong> ${header.keterangan}</div>` : ''}
                `;
                
                $('#modalDetailContent').html(modalContent);
                $('#modalDetailTransaksi').modal('show');
            } else {
                showError(response.message || 'Gagal memuat detail transaksi');
            }
        },
        function(message) {
            showError('Terjadi kesalahan: ' + message);
        }
    );
}

function printStruk(kd_penjualan) {
    // Open print window
    const printUrl = `print.php?kd_penjualan=${kd_penjualan}`;
    window.open(printUrl, '_blank', 'width=800,height=600');
}

function exportData() {
    // Get current filter values
    const filterData = {
        tanggal_dari: $('#filter_tanggal_dari').val(),
        tanggal_sampai: $('#filter_tanggal_sampai').val(),
        pelanggan: $('#filter_pelanggan').val(),
        kasir: $('#filter_kasir').val(),
        cara_bayar: $('#filter_cara_bayar').val(),
        no_transaksi: $('#filter_no_transaksi').val(),
        status: $('#filter_status').val()
    };
    
    // Create form and submit
    const form = $('<form>', {
        method: 'POST',
        action: 'export.php',
        target: '_blank'
    });
    
    Object.keys(filterData).forEach(function(key) {
        if (filterData[key]) {
            form.append($('<input>', {
                type: 'hidden',
                name: key,
                value: filterData[key]
            }));
        }
    });
    
    $('body').append(form);
    form.submit();
    form.remove();
}

// Helper functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID');
}

function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });
}

function formatRupiah(amount) {
    return 'Rp ' + parseInt(amount).toLocaleString('id-ID');
}
