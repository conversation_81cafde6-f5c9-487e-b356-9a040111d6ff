<?php
/**
 * Session Management
 * KOPEGBI POS System
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['username']);
}

// Function to check user level
function isAdmin() {
    return isset($_SESSION['level']) && $_SESSION['level'] === 'admin';
}

function isUser() {
    return isset($_SESSION['level']) && $_SESSION['level'] === 'user';
}

// Function to get current user info
function getCurrentUser() {
    if (isLoggedIn()) {
        return [
            'kd_user' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'nama_user' => $_SESSION['nama_user'],
            'level' => $_SESSION['level']
        ];
    }
    return null;
}

// Function to login user
function loginUser($user_data) {
    $_SESSION['user_id'] = $user_data['kd_user'];
    $_SESSION['username'] = $user_data['username'];
    $_SESSION['nama_user'] = $user_data['nama_user'];
    $_SESSION['level'] = $user_data['level'];
    $_SESSION['login_time'] = time();
}

// Function to logout user
function logoutUser() {
    session_unset();
    session_destroy();
}

// Function to require login (redirect to login if not logged in)
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

// Function to require admin access
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: dashboard.php?error=access_denied');
        exit();
    }
}

// Function to prevent access to login page if already logged in
function preventLoginAccess() {
    if (isLoggedIn()) {
        header('Location: dashboard.php');
        exit();
    }
}

// Function to get session timeout (in seconds)
function getSessionTimeout() {
    return 3600; // 1 hour
}

// Function to check session timeout
function checkSessionTimeout() {
    if (isLoggedIn() && isset($_SESSION['login_time'])) {
        if (time() - $_SESSION['login_time'] > getSessionTimeout()) {
            logoutUser();
            return false;
        }
        // Update last activity time
        $_SESSION['login_time'] = time();
    }
    return true;
}
?>
