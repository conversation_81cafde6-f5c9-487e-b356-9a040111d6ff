<?php
require_once '../config/database.php';
require_once '../config/session.php';

// Check if user is logged in
requireLogin();

$kd_penjualan = (int)($_GET['id'] ?? 0);

if ($kd_penjualan <= 0) {
    die('ID penjualan tidak valid.');
}

$connection = getConnection();

// Get transaction header
$header_query = "SELECT p.*, pel.nama_pelanggan, u.nama_user 
                 FROM penjualan p 
                 LEFT JOIN pelanggan pel ON p.kd_pelanggan = pel.kd_pelanggan 
                 JOIN user u ON p.kd_user = u.kd_user 
                 WHERE p.kd_penjualan = ?";
$header_stmt = $connection->prepare($header_query);
$header_stmt->bind_param("i", $kd_penjualan);
$header_stmt->execute();
$header_result = $header_stmt->get_result();

if ($header_result->num_rows == 0) {
    die('Transaksi tidak ditemukan.');
}

$header = $header_result->fetch_assoc();
$header_stmt->close();

// Get transaction items
$items_query = "SELECT pi.*, b.nama_barang, s.nama_satuan 
                FROM penjualan_item pi 
                JOIN barang b ON pi.kd_barang = b.kd_barang 
                JOIN satuan s ON b.kd_satuan = s.kd_satuan 
                WHERE pi.kd_penjualan = ? 
                ORDER BY b.nama_barang ASC";
$items_stmt = $connection->prepare($items_query);
$items_stmt->bind_param("i", $kd_penjualan);
$items_stmt->execute();
$items_result = $items_stmt->get_result();

$items = [];
while ($row = $items_result->fetch_assoc()) {
    $items[] = $row;
}
$items_stmt->close();

closeConnection($connection);

// Calculate totals
$subtotal = 0;
$total_diskon = 0;
$total_qty = 0;

foreach ($items as $item) {
    $item_subtotal = $item['harga_jual'] * $item['jumlah'];
    $item_diskon = $item_subtotal * $item['disc'] / 100;
    
    $subtotal += $item_subtotal;
    $total_diskon += $item_diskon;
    $total_qty += $item['jumlah'];
}

$grand_total = $subtotal - $total_diskon;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk Penjualan - <?php echo $header['no_transaksi']; ?></title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
            margin: 0;
            padding: 10px;
            width: 80mm;
            background: white;
        }
        
        .receipt {
            width: 100%;
        }
        
        .header {
            text-align: center;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }
        
        .header p {
            margin: 2px 0;
            font-size: 10px;
        }
        
        .transaction-info {
            margin-bottom: 10px;
            font-size: 10px;
        }
        
        .transaction-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .items {
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .item {
            margin-bottom: 8px;
        }
        
        .item-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .item-details {
            display: flex;
            justify-content: space-between;
            font-size: 10px;
        }
        
        .totals {
            margin-bottom: 10px;
        }
        
        .totals div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
        }
        
        .payment {
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .payment div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .footer {
            text-align: center;
            font-size: 10px;
        }
        
        .footer p {
            margin: 2px 0;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- Header -->
        <div class="header">
            <h1>KOPEGBI</h1>
            <p>Sistem Point of Sale</p>
            <p>Terima kasih atas kunjungan Anda</p>
        </div>
        
        <!-- Transaction Info -->
        <div class="transaction-info">
            <div>
                <span>No. Transaksi:</span>
                <span><?php echo htmlspecialchars($header['no_transaksi']); ?></span>
            </div>
            <div>
                <span>Tanggal:</span>
                <span><?php echo formatDateTime($header['tgl_penjualan']); ?></span>
            </div>
            <div>
                <span>Kasir:</span>
                <span><?php echo htmlspecialchars($header['nama_user']); ?></span>
            </div>
            <?php if ($header['nama_pelanggan']): ?>
            <div>
                <span>Pelanggan:</span>
                <span><?php echo htmlspecialchars($header['nama_pelanggan']); ?></span>
            </div>
            <?php endif; ?>
            <div>
                <span>Cara Bayar:</span>
                <span><?php echo htmlspecialchars($header['cara_bayar']); ?></span>
            </div>
        </div>
        
        <!-- Items -->
        <div class="items">
            <?php foreach ($items as $item): ?>
                <?php
                $item_subtotal = $item['harga_jual'] * $item['jumlah'];
                $item_diskon = $item_subtotal * $item['disc'] / 100;
                $item_total = $item_subtotal - $item_diskon;
                ?>
                <div class="item">
                    <div class="item-name"><?php echo htmlspecialchars($item['nama_barang']); ?></div>
                    <div class="item-details">
                        <span><?php echo $item['jumlah']; ?> <?php echo htmlspecialchars($item['nama_satuan']); ?> x <?php echo formatRupiah($item['harga_jual']); ?></span>
                        <span><?php echo formatRupiah($item_total); ?></span>
                    </div>
                    <?php if ($item['disc'] > 0): ?>
                    <div class="item-details">
                        <span>Diskon <?php echo $item['disc']; ?>%</span>
                        <span>-<?php echo formatRupiah($item_diskon); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Totals -->
        <div class="totals">
            <div>
                <span>Total Item:</span>
                <span><?php echo count($items); ?></span>
            </div>
            <div>
                <span>Total Qty:</span>
                <span><?php echo $total_qty; ?></span>
            </div>
            <div>
                <span>Subtotal:</span>
                <span><?php echo formatRupiah($subtotal); ?></span>
            </div>
            <?php if ($total_diskon > 0): ?>
            <div>
                <span>Total Diskon:</span>
                <span>-<?php echo formatRupiah($total_diskon); ?></span>
            </div>
            <?php endif; ?>
            <div class="grand-total">
                <span>TOTAL:</span>
                <span><?php echo formatRupiah($grand_total); ?></span>
            </div>
        </div>
        
        <!-- Payment -->
        <div class="payment">
            <div>
                <span>Dibayar:</span>
                <span><?php echo formatRupiah($header['dibayar']); ?></span>
            </div>
            <div>
                <span>Kembalian:</span>
                <span><?php echo formatRupiah($header['kembalian']); ?></span>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>Barang yang sudah dibeli</p>
            <p>tidak dapat dikembalikan</p>
            <p>kecuali ada perjanjian</p>
            <br>
            <p>Terima kasih</p>
            <p><?php echo date('d/m/Y H:i:s'); ?></p>
        </div>
    </div>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
