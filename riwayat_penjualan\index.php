<?php
$page_title = 'Riwayat Transaksi Penjualan';
require_once '../config/database.php';
require_once '../config/session.php';

// Check if user is logged in
requireLogin();

require_once '../includes/header.php';

$connection = getConnection();

// Get filter options
$pelanggan_query = "SELECT kd_pelanggan, nama_pelanggan FROM pelanggan ORDER BY nama_pelanggan";
$pelanggan_result = $connection->query($pelanggan_query);

$users_query = "SELECT kd_user, nama_user FROM user ORDER BY nama_user";
$users_result = $connection->query($users_query);

$pembayaran_query = "SELECT DISTINCT cara_bayar FROM pembayaran ORDER BY cara_bayar";
$pembayaran_result = $connection->query($pembayaran_query);

closeConnection($connection);
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Riwayat Transaksi Penjualan
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Hari Ini</h6>
                                            <h4 id="summary_today">-</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-day fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Minggu Ini</h6>
                                            <h4 id="summary_week">-</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-week fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Bulan Ini</h6>
                                            <h4 id="summary_month">-</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-filter me-2"></i>Filter & Pencarian
                            </h6>
                        </div>
                        <div class="card-body">
                            <form id="filterForm">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="filter_tanggal_dari" class="form-label">Tanggal Dari</label>
                                        <input type="date" class="form-control" id="filter_tanggal_dari" name="tanggal_dari" value="<?php echo date('Y-m-01'); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="filter_tanggal_sampai" class="form-label">Tanggal Sampai</label>
                                        <input type="date" class="form-control" id="filter_tanggal_sampai" name="tanggal_sampai" value="<?php echo date('Y-m-d'); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="filter_pelanggan" class="form-label">Pelanggan</label>
                                        <select class="form-select" id="filter_pelanggan" name="pelanggan">
                                            <option value="">Semua Pelanggan</option>
                                            <?php while ($pelanggan = $pelanggan_result->fetch_assoc()): ?>
                                                <option value="<?php echo $pelanggan['kd_pelanggan']; ?>">
                                                    <?php echo htmlspecialchars($pelanggan['nama_pelanggan']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="filter_kasir" class="form-label">Kasir</label>
                                        <select class="form-select" id="filter_kasir" name="kasir">
                                            <option value="">Semua Kasir</option>
                                            <?php while ($user = $users_result->fetch_assoc()): ?>
                                                <option value="<?php echo $user['kd_user']; ?>">
                                                    <?php echo htmlspecialchars($user['nama_user']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-3">
                                        <label for="filter_cara_bayar" class="form-label">Cara Bayar</label>
                                        <select class="form-select" id="filter_cara_bayar" name="cara_bayar">
                                            <option value="">Semua Cara Bayar</option>
                                            <?php while ($pembayaran = $pembayaran_result->fetch_assoc()): ?>
                                                <option value="<?php echo htmlspecialchars($pembayaran['cara_bayar']); ?>">
                                                    <?php echo htmlspecialchars($pembayaran['cara_bayar']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="filter_no_transaksi" class="form-label">No. Transaksi</label>
                                        <input type="text" class="form-control" id="filter_no_transaksi" name="no_transaksi" placeholder="Cari nomor transaksi...">
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button type="button" class="btn btn-primary me-2" id="btnFilter">
                                            <i class="fas fa-search me-1"></i>Filter
                                        </button>
                                        <button type="button" class="btn btn-secondary me-2" id="btnReset">
                                            <i class="fas fa-refresh me-1"></i>Reset
                                        </button>
                                        <button type="button" class="btn btn-success" id="btnExport">
                                            <i class="fas fa-download me-1"></i>Export
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table id="tableRiwayat" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>No. Transaksi</th>
                                    <th>Tanggal</th>
                                    <th>Pelanggan</th>
                                    <th>Dibayar</th>
                                    <th>Kembalian</th>
                                    <th>Cara Bayar</th>
                                    <th>Kasir</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Transaksi -->
<div class="modal fade" id="modalDetailTransaksi" tabindex="-1" aria-labelledby="modalDetailTransaksiLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailTransaksiLabel">
                    <i class="fas fa-receipt me-2"></i>Detail Transaksi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="modalDetailContent">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<?php
$page_scripts = ['riwayat_penjualan/js/riwayat.js'];
require_once '../includes/footer.php';
?>
