<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is logged in
requireLogin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add':
            $barcode = trim($_POST['barcode'] ?? '');
            $nama_barang = trim($_POST['nama_barang'] ?? '');
            $kd_kategori = (int)($_POST['kd_kategori'] ?? 0);
            $kd_merk = (int)($_POST['kd_merk'] ?? 0);
            $kd_satuan = (int)($_POST['kd_satuan'] ?? 0);
            $stock = (int)($_POST['stock'] ?? 0);
            $harga_beli = (int)str_replace('.', '', $_POST['harga_beli'] ?? '0');
            $harga_jual = (int)str_replace('.', '', $_POST['harga_jual'] ?? '0');
            $keterangan = trim($_POST['keterangan'] ?? '');

            // Validation
            if (empty($barcode)) {
                $response['message'] = 'Barcode harus diisi.';
                break;
            }

            if (empty($nama_barang)) {
                $response['message'] = 'Nama barang harus diisi.';
                break;
            }

            if ($kd_kategori <= 0) {
                $response['message'] = 'Kategori harus dipilih.';
                break;
            }

            if ($kd_merk <= 0) {
                $response['message'] = 'Merk harus dipilih.';
                break;
            }

            if ($kd_satuan <= 0) {
                $response['message'] = 'Satuan harus dipilih.';
                break;
            }

            if ($stock < 0) {
                $response['message'] = 'Stock tidak boleh negatif.';
                break;
            }

            if ($harga_beli <= 0) {
                $response['message'] = 'Harga beli harus lebih dari 0.';
                break;
            }

            if ($harga_jual <= 0) {
                $response['message'] = 'Harga jual harus lebih dari 0.';
                break;
            }

            // Check if barcode already exists
            $check_query = "SELECT kd_barang FROM barang WHERE barcode = ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("s", $barcode);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $response['message'] = 'Barcode sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();

            // Check if nama barang already exists
            $check_query = "SELECT kd_barang FROM barang WHERE nama_barang = ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("s", $nama_barang);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama barang sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();

            // Insert new barang
            $insert_query = "INSERT INTO barang (barcode, nama_barang, kd_merk, kd_kategori, kd_satuan, stock, harga_beli, harga_jual, keterangan) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $insert_stmt = $connection->prepare($insert_query);
            $insert_stmt->bind_param("ssiiiiiis", $barcode, $nama_barang, $kd_merk, $kd_kategori, $kd_satuan, $stock, $harga_beli, $harga_jual, $keterangan);

            if ($insert_stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'Barang berhasil ditambahkan.';
            } else {
                $response['message'] = 'Gagal menambahkan barang.';
            }
            $insert_stmt->close();
            break;

        case 'edit':
            $kd_barang = (int)($_POST['kd_barang'] ?? 0);
            $barcode = trim($_POST['barcode'] ?? '');
            $nama_barang = trim($_POST['nama_barang'] ?? '');
            $kd_kategori = (int)($_POST['kd_kategori'] ?? 0);
            $kd_merk = (int)($_POST['kd_merk'] ?? 0);
            $kd_satuan = (int)($_POST['kd_satuan'] ?? 0);
            $stock = (int)($_POST['stock'] ?? 0);
            $harga_beli = (int)str_replace('.', '', $_POST['harga_beli'] ?? '0');
            $harga_jual = (int)str_replace('.', '', $_POST['harga_jual'] ?? '0');
            $keterangan = trim($_POST['keterangan'] ?? '');

            // Validation
            if ($kd_barang <= 0) {
                $response['message'] = 'ID barang tidak valid.';
                break;
            }

            if (empty($barcode)) {
                $response['message'] = 'Barcode harus diisi.';
                break;
            }

            if (empty($nama_barang)) {
                $response['message'] = 'Nama barang harus diisi.';
                break;
            }

            if ($kd_kategori <= 0) {
                $response['message'] = 'Kategori harus dipilih.';
                break;
            }

            if ($kd_merk <= 0) {
                $response['message'] = 'Merk harus dipilih.';
                break;
            }

            if ($kd_satuan <= 0) {
                $response['message'] = 'Satuan harus dipilih.';
                break;
            }

            if ($stock < 0) {
                $response['message'] = 'Stock tidak boleh negatif.';
                break;
            }

            if ($harga_beli <= 0) {
                $response['message'] = 'Harga beli harus lebih dari 0.';
                break;
            }

            if ($harga_jual <= 0) {
                $response['message'] = 'Harga jual harus lebih dari 0.';
                break;
            }

            // Check if barcode already exists (except current record)
            $check_query = "SELECT kd_barang FROM barang WHERE barcode = ? AND kd_barang != ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("si", $barcode, $kd_barang);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $response['message'] = 'Barcode sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();

            // Check if nama barang already exists (except current record)
            $check_query = "SELECT kd_barang FROM barang WHERE nama_barang = ? AND kd_barang != ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("si", $nama_barang, $kd_barang);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama barang sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();

            // Update barang
            $update_query = "UPDATE barang SET barcode = ?, nama_barang = ?, kd_merk = ?, kd_kategori = ?, kd_satuan = ?, stock = ?, harga_beli = ?, harga_jual = ?, keterangan = ? WHERE kd_barang = ?";
            $update_stmt = $connection->prepare($update_query);
            $update_stmt->bind_param("ssiiiiiisi", $barcode, $nama_barang, $kd_merk, $kd_kategori, $kd_satuan, $stock, $harga_beli, $harga_jual, $keterangan, $kd_barang);

            if ($update_stmt->execute()) {
                if ($update_stmt->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Barang berhasil diperbarui.';
                } else {
                    $response['message'] = 'Tidak ada perubahan data.';
                }
            } else {
                $response['message'] = 'Gagal memperbarui barang.';
            }
            $update_stmt->close();
            break;

        case 'get':
            $kd_barang = (int)($_POST['kd_barang'] ?? 0);

            if ($kd_barang <= 0) {
                $response['message'] = 'ID barang tidak valid.';
                break;
            }

            $query = "SELECT * FROM barang WHERE kd_barang = ?";
            $stmt = $connection->prepare($query);
            $stmt->bind_param("i", $kd_barang);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $response['success'] = true;
                $response['data'] = $result->fetch_assoc();
            } else {
                $response['message'] = 'Barang tidak ditemukan.';
            }
            $stmt->close();
            break;

        case 'generate_barcode':
            // Generate unique barcode
            $barcode = '';
            do {
                $barcode = date('Ymd') . sprintf('%06d', rand(1, 999999));
                $check_query = "SELECT kd_barang FROM barang WHERE barcode = ?";
                $check_stmt = $connection->prepare($check_query);
                $check_stmt->bind_param("s", $barcode);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $exists = $check_result->num_rows > 0;
                $check_stmt->close();
            } while ($exists);

            $response['success'] = true;
            $response['barcode'] = $barcode;
            break;

        default:
            $response['message'] = 'Aksi tidak valid.';
            break;
    }
}

// Handle DataTables server-side processing
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['draw'])) {
    $draw = (int)$_GET['draw'];
    $start = (int)$_GET['start'];
    $length = (int)$_GET['length'];
    $search = $_GET['search']['value'] ?? '';

    // Base query with joins
    $base_query = "FROM barang b
                   JOIN kategori k ON b.kd_kategori = k.kd_kategori
                   JOIN merk m ON b.kd_merk = m.kd_merk
                   JOIN satuan s ON b.kd_satuan = s.kd_satuan";
    $where_clause = "";
    $params = [];
    $param_types = "";

    // Search functionality
    if (!empty($search)) {
        $where_clause = " WHERE b.barcode LIKE ? OR b.nama_barang LIKE ? OR k.nama_kategori LIKE ? OR m.nama_merk LIKE ? OR s.nama_satuan LIKE ?";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $param_types .= "sssss";
    }

    // Count total records
    $count_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    $count_stmt = $connection->prepare($count_query);
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();

    // Get data with pagination
    $data_query = "SELECT b.kd_barang, b.barcode, b.nama_barang, k.nama_kategori, m.nama_merk, s.nama_satuan, b.stock, b.harga_jual " . $base_query . $where_clause . " ORDER BY b.nama_barang ASC LIMIT ?, ?";
    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";

    $data_stmt = $connection->prepare($data_query);
    $data_stmt->bind_param($param_types, ...$params);
    $data_stmt->execute();
    $result = $data_stmt->get_result();

    $data = [];
    $no = $start + 1;
    while ($row = $result->fetch_assoc()) {
        $stock_badge = '';
        if ($row['stock'] <= 5) {
            $stock_badge = 'bg-danger';
        } elseif ($row['stock'] <= 10) {
            $stock_badge = 'bg-warning';
        } else {
            $stock_badge = 'bg-success';
        }

        $data[] = [
            $no++,
            htmlspecialchars($row['barcode']),
            htmlspecialchars($row['nama_barang']),
            htmlspecialchars($row['nama_kategori']),
            htmlspecialchars($row['nama_merk']),
            htmlspecialchars($row['nama_satuan']),
            '<span class="badge ' . $stock_badge . '">' . number_format($row['stock']) . '</span>',
            'Rp ' . number_format($row['harga_jual'], 0, ',', '.'),
            '<div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-info btn-view" data-id="' . $row['kd_barang'] . '" title="Lihat Detail">
                    <i class="fas fa-eye"></i>
                </button>
                <button type="button" class="btn btn-sm btn-warning btn-edit" data-id="' . $row['kd_barang'] . '" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <a href="?action=delete&id=' . $row['kd_barang'] . '" class="btn btn-sm btn-danger btn-delete" title="Hapus" data-message="Apakah Anda yakin ingin menghapus barang ' . htmlspecialchars($row['nama_barang']) . '?">
                    <i class="fas fa-trash"></i>
                </a>
            </div>'
        ];
    }
    $data_stmt->close();

    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ];
}

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
