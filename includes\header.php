<?php
// Determine the correct path to config files
$config_path = '';
if (strpos($_SERVER['REQUEST_URI'], '/kategori/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/merk/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/satuan/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/suplier/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/pelanggan/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/user/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/barang/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/pembelian/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/penjualan/') !== false ||
    strpos($_SERVER['REQUEST_URI'], '/retur_pembelian/') !== false) {
    $config_path = '../';
}

require_once $config_path . 'config/session.php';
requireLogin();
checkSessionTimeout();
$current_user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>KOPEGBI POS</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <!-- Custom CSS -->
    <link href="<?php echo $config_path; ?>assets/css/style.css" rel="stylesheet">

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo $config_path; ?>dashboard.php">
                <i class="fas fa-cash-register me-2"></i>KOPEGBI POS
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo $config_path; ?>dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>

                    <!-- Master Data Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-database me-1"></i>Master Data
                        </a>
                        <ul class="dropdown-menu">
                            <?php if (isAdmin()): ?>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>user/"><i class="fas fa-users me-2"></i>User</a></li>
                            <?php endif; ?>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>kategori/"><i class="fas fa-tags me-2"></i>Kategori</a></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>merk/"><i class="fas fa-trademark me-2"></i>Merk</a></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>satuan/"><i class="fas fa-balance-scale me-2"></i>Satuan</a></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>suplier/"><i class="fas fa-truck me-2"></i>Supplier</a></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>pelanggan/"><i class="fas fa-user-friends me-2"></i>Pelanggan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>barang/"><i class="fas fa-box me-2"></i>Barang</a></li>
                        </ul>
                    </li>

                    <!-- Transaksi Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-exchange-alt me-1"></i>Transaksi
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>penjualan/"><i class="fas fa-shopping-cart me-2"></i>Penjualan</a></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>riwayat_penjualan/"><i class="fas fa-history me-2"></i>Riwayat Penjualan</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>pembelian/"><i class="fas fa-shopping-bag me-2"></i>Pembelian</a></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>retur_pembelian/"><i class="fas fa-undo me-2"></i>Retur Pembelian</a></li>
                        </ul>
                    </li>
                </ul>

                <!-- User Info -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($current_user['nama_user']); ?>
                            <span class="badge bg-secondary ms-1"><?php echo ucfirst($current_user['level']); ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo $config_path; ?>logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid" style="margin-top: 76px;">
