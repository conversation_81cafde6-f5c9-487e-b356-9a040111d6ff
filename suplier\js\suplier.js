/**
 * Supplier Management JavaScript
 */

$(document).ready(function() {
    // Initialize DataTable
    var table = initDataTable('#tableSuplier', {
        ajax: {
            url: 'ajax/suplier_ajax.php',
            type: 'GET'
        },
        columns: [
            { data: 0, orderable: false },
            { data: 1 },
            { data: 2 },
            { data: 3 },
            { data: 4, orderable: false, searchable: false }
        ],
        order: [[1, 'asc']]
    });
    
    // Handle form submission
    $('#formSuplier').submit(function(e) {
        e.preventDefault();
        
        if (!validateForm('#formSuplier')) {
            return false;
        }
        
        var formData = $(this).serialize();
        var btnSimpan = $('#btnSimpan');
        var originalText = btnSimpan.html();
        
        // Show loading
        showLoading(btnSimpan);
        
        ajaxRequest(
            'ajax/suplier_ajax.php',
            formData,
            function(response) {
                // Success callback
                hideLoading(btnSimpan, originalText);
                showSuccess(response.message);
                $('#modalSuplier').modal('hide');
                table.ajax.reload();
                resetForm('#formSuplier');
            },
            function(message) {
                // Error callback
                hideLoading(btnSimpan, originalText);
                showError(message);
            }
        );
    });
    
    // Handle edit button click
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        
        // Reset form
        resetForm('#formSuplier');
        
        // Change modal title and action
        $('#modalSuplierLabel').html('<i class="fas fa-edit me-2"></i>Edit Supplier');
        $('#action').val('edit');
        
        // Get supplier data
        ajaxRequest(
            'ajax/suplier_ajax.php',
            { action: 'get', kd_suplier: id },
            function(response) {
                // Fill form with data
                $('#kd_suplier').val(response.data.kd_suplier);
                $('#nama_suplier').val(response.data.nama_suplier);
                $('#alamat_suplier').val(response.data.alamat_suplier);
                $('#tlp_suplier').val(response.data.tlp_suplier);
                
                // Show modal
                $('#modalSuplier').modal('show');
            },
            function(message) {
                showError(message);
            }
        );
    });
    
    // Reset modal when closed
    $('#modalSuplier').on('hidden.bs.modal', function() {
        resetForm('#formSuplier');
        $('#modalSuplierLabel').html('<i class="fas fa-plus me-2"></i>Tambah Supplier');
        $('#action').val('add');
        $('#kd_suplier').val('');
    });
    
    // Handle form validation
    $('#nama_suplier, #alamat_suplier, #tlp_suplier').on('input', function() {
        var value = $(this).val().trim();
        if (value.length > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Phone number validation
    $('#tlp_suplier').on('input', function() {
        var value = $(this).val().trim();
        var phoneRegex = /^[0-9+\-\s()]+$/;
        
        if (value.length > 0 && phoneRegex.test(value)) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else if (value.length > 0) {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Auto-focus on modal show
    $('#modalSuplier').on('shown.bs.modal', function() {
        $('#nama_suplier').focus();
    });
});
