<?php
$page_title = 'Dashboard';
require_once 'config/database.php';
require_once 'includes/header.php';

// Get statistics
$connection = getConnection();

// Count total barang
$query = "SELECT COUNT(*) as total FROM barang";
$result = $connection->query($query);
$total_barang = $result->fetch_assoc()['total'];

// Count total pelanggan
$query = "SELECT COUNT(*) as total FROM pelanggan";
$result = $connection->query($query);
$total_pelanggan = $result->fetch_assoc()['total'];

// Count total supplier
$query = "SELECT COUNT(*) as total FROM suplier";
$result = $connection->query($query);
$total_supplier = $result->fetch_assoc()['total'];

// Count today's sales
$query = "SELECT COUNT(*) as total FROM penjualan WHERE DATE(tgl_penjualan) = CURDATE()";
$result = $connection->query($query);
$penjualan_hari_ini = $result->fetch_assoc()['total'];

// Get today's sales amount
$query = "SELECT COALESCE(SUM(pi.harga_jual * pi.jumlah), 0) as total 
          FROM penjualan p 
          JOIN penjualan_item pi ON p.kd_penjualan = pi.kd_penjualan 
          WHERE DATE(p.tgl_penjualan) = CURDATE()";
$result = $connection->query($query);
$omzet_hari_ini = $result->fetch_assoc()['total'];

// Get low stock items (stock <= 10)
$query = "SELECT b.nama_barang, b.stock, s.nama_satuan 
          FROM barang b 
          JOIN satuan s ON b.kd_satuan = s.kd_satuan 
          WHERE b.stock <= 10 
          ORDER BY b.stock ASC 
          LIMIT 10";
$low_stock_result = $connection->query($query);

// Get recent sales
$query = "SELECT p.no_transaksi, p.tgl_penjualan, pel.nama_pelanggan, u.nama_user,
          (SELECT SUM(pi.harga_jual * pi.jumlah) FROM penjualan_item pi WHERE pi.kd_penjualan = p.kd_penjualan) as total
          FROM penjualan p
          LEFT JOIN pelanggan pel ON p.kd_pelanggan = pel.kd_pelanggan
          JOIN user u ON p.kd_user = u.kd_user
          ORDER BY p.tgl_penjualan DESC
          LIMIT 10";
$recent_sales_result = $connection->query($query);

closeConnection($connection);
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-number"><?php echo number_format($total_barang); ?></div>
                    <div class="stats-label">Total Barang</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-box stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-number"><?php echo number_format($total_pelanggan); ?></div>
                    <div class="stats-label">Total Pelanggan</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-user-friends stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-number"><?php echo number_format($total_supplier); ?></div>
                    <div class="stats-label">Total Supplier</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-truck stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card info">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-number"><?php echo number_format($penjualan_hari_ini); ?></div>
                    <div class="stats-label">Penjualan Hari Ini</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-shopping-cart stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Omzet Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <h4 class="card-title text-primary">
                    <i class="fas fa-money-bill-wave me-2"></i>Omzet Hari Ini
                </h4>
                <h2 class="text-success mb-0"><?php echo formatRupiah($omzet_hari_ini); ?></h2>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Low Stock Alert -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Stok Menipis
                </h5>
            </div>
            <div class="card-body">
                <?php if ($low_stock_result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Nama Barang</th>
                                    <th>Stok</th>
                                    <th>Satuan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($item = $low_stock_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['nama_barang']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $item['stock'] <= 5 ? 'danger' : 'warning'; ?>">
                                            <?php echo $item['stock']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($item['nama_satuan']); ?></td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted mb-0">Tidak ada barang dengan stok menipis.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Recent Sales -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart text-success me-2"></i>
                    Penjualan Terbaru
                </h5>
            </div>
            <div class="card-body">
                <?php if ($recent_sales_result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>No. Transaksi</th>
                                    <th>Tanggal</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($sale = $recent_sales_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($sale['no_transaksi']); ?></td>
                                    <td><?php echo formatDate($sale['tgl_penjualan']); ?></td>
                                    <td><?php echo formatRupiah($sale['total']); ?></td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted mb-0">Belum ada transaksi penjualan.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
