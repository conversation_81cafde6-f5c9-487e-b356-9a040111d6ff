<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is logged in
requireLogin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            $nama_suplier = trim($_POST['nama_suplier'] ?? '');
            $alamat_suplier = trim($_POST['alamat_suplier'] ?? '');
            $tlp_suplier = trim($_POST['tlp_suplier'] ?? '');
            
            if (empty($nama_suplier)) {
                $response['message'] = 'Nama supplier harus diisi.';
                break;
            }
            
            if (empty($alamat_suplier)) {
                $response['message'] = 'Alamat supplier harus diisi.';
                break;
            }
            
            if (empty($tlp_suplier)) {
                $response['message'] = 'Telepon supplier harus diisi.';
                break;
            }
            
            // Check if supplier already exists
            $check_query = "SELECT kd_suplier FROM suplier WHERE nama_suplier = ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("s", $nama_suplier);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama supplier sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Insert new supplier
            $insert_query = "INSERT INTO suplier (nama_suplier, alamat_suplier, tlp_suplier) VALUES (?, ?, ?)";
            $insert_stmt = $connection->prepare($insert_query);
            $insert_stmt->bind_param("sss", $nama_suplier, $alamat_suplier, $tlp_suplier);
            
            if ($insert_stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'Supplier berhasil ditambahkan.';
            } else {
                $response['message'] = 'Gagal menambahkan supplier.';
            }
            $insert_stmt->close();
            break;
            
        case 'edit':
            $kd_suplier = (int)($_POST['kd_suplier'] ?? 0);
            $nama_suplier = trim($_POST['nama_suplier'] ?? '');
            $alamat_suplier = trim($_POST['alamat_suplier'] ?? '');
            $tlp_suplier = trim($_POST['tlp_suplier'] ?? '');
            
            if (empty($nama_suplier)) {
                $response['message'] = 'Nama supplier harus diisi.';
                break;
            }
            
            if (empty($alamat_suplier)) {
                $response['message'] = 'Alamat supplier harus diisi.';
                break;
            }
            
            if (empty($tlp_suplier)) {
                $response['message'] = 'Telepon supplier harus diisi.';
                break;
            }
            
            if ($kd_suplier <= 0) {
                $response['message'] = 'ID supplier tidak valid.';
                break;
            }
            
            // Check if supplier name already exists (except current record)
            $check_query = "SELECT kd_suplier FROM suplier WHERE nama_suplier = ? AND kd_suplier != ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("si", $nama_suplier, $kd_suplier);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama supplier sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Update supplier
            $update_query = "UPDATE suplier SET nama_suplier = ?, alamat_suplier = ?, tlp_suplier = ? WHERE kd_suplier = ?";
            $update_stmt = $connection->prepare($update_query);
            $update_stmt->bind_param("sssi", $nama_suplier, $alamat_suplier, $tlp_suplier, $kd_suplier);
            
            if ($update_stmt->execute()) {
                if ($update_stmt->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Supplier berhasil diperbarui.';
                } else {
                    $response['message'] = 'Tidak ada perubahan data.';
                }
            } else {
                $response['message'] = 'Gagal memperbarui supplier.';
            }
            $update_stmt->close();
            break;
            
        case 'get':
            $kd_suplier = (int)($_POST['kd_suplier'] ?? 0);
            
            if ($kd_suplier <= 0) {
                $response['message'] = 'ID supplier tidak valid.';
                break;
            }
            
            $query = "SELECT * FROM suplier WHERE kd_suplier = ?";
            $stmt = $connection->prepare($query);
            $stmt->bind_param("i", $kd_suplier);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $response['success'] = true;
                $response['data'] = $result->fetch_assoc();
            } else {
                $response['message'] = 'Supplier tidak ditemukan.';
            }
            $stmt->close();
            break;
            
        default:
            $response['message'] = 'Aksi tidak valid.';
            break;
    }
}

// Handle DataTables server-side processing
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['draw'])) {
    $draw = (int)$_GET['draw'];
    $start = (int)$_GET['start'];
    $length = (int)$_GET['length'];
    $search = $_GET['search']['value'] ?? '';
    
    // Base query
    $base_query = "FROM suplier";
    $where_clause = "";
    $params = [];
    $param_types = "";
    
    // Search functionality
    if (!empty($search)) {
        $where_clause = " WHERE nama_suplier LIKE ? OR alamat_suplier LIKE ? OR tlp_suplier LIKE ?";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $param_types .= "sss";
    }
    
    // Count total records
    $count_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    $count_stmt = $connection->prepare($count_query);
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
    
    // Get data with pagination
    $data_query = "SELECT kd_suplier, nama_suplier, alamat_suplier, tlp_suplier " . $base_query . $where_clause . " ORDER BY nama_suplier ASC LIMIT ?, ?";
    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";
    
    $data_stmt = $connection->prepare($data_query);
    $data_stmt->bind_param($param_types, ...$params);
    $data_stmt->execute();
    $result = $data_stmt->get_result();
    
    $data = [];
    $no = $start + 1;
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            $no++,
            htmlspecialchars($row['nama_suplier']),
            htmlspecialchars($row['alamat_suplier']),
            htmlspecialchars($row['tlp_suplier']),
            '<div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-warning btn-edit" data-id="' . $row['kd_suplier'] . '" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <a href="?action=delete&id=' . $row['kd_suplier'] . '" class="btn btn-sm btn-danger btn-delete" title="Hapus" data-message="Apakah Anda yakin ingin menghapus supplier ' . htmlspecialchars($row['nama_suplier']) . '?">
                    <i class="fas fa-trash"></i>
                </a>
            </div>'
        ];
    }
    $data_stmt->close();
    
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ];
}

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
?>
