<?php
/**
 * Database Configuration
 * KOPEGBI POS System
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '8@n90N3!');
define('DB_NAME', 'kopegbi');

// Create connection
function getConnection() {
    $connection = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    // Check connection
    if ($connection->connect_error) {
        die("Connection failed: " . $connection->connect_error);
    }
    
    // Set charset to utf8
    $connection->set_charset("utf8");
    
    return $connection;
}

// Function to close connection
function closeConnection($connection) {
    if ($connection) {
        $connection->close();
    }
}

// Function to escape string
function escapeString($connection, $string) {
    return $connection->real_escape_string($string);
}

// Function to format currency (Rupiah)
function formatRupiah($amount) {
    return 'Rp ' . number_format($amount, 0, ',', '.');
}

// Function to format date
function formatDate($date) {
    return date('d/m/Y', strtotime($date));
}

// Function to format datetime
function formatDateTime($datetime) {
    return date('d/m/Y H:i:s', strtotime($datetime));
}

// Function to generate transaction number
function generateTransactionNumber($prefix = 'TRX') {
    return $prefix . date('Ymd') . sprintf('%04d', rand(1, 9999));
}
?>
