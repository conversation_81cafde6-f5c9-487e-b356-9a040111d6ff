/**
 * Barang Management JavaScript
 */

$(document).ready(function() {
    // Initialize DataTable
    var table = initDataTable('#tableBarang', {
        ajax: {
            url: 'ajax/barang_ajax.php',
            type: 'GET'
        },
        columns: [
            { data: 0, orderable: false },
            { data: 1 },
            { data: 2 },
            { data: 3 },
            { data: 4 },
            { data: 5 },
            { data: 6, orderable: false },
            { data: 7, orderable: false },
            { data: 8, orderable: false, searchable: false }
        ],
        order: [[2, 'asc']]
    });
    
    // Handle form submission
    $('#formBarang').submit(function(e) {
        e.preventDefault();
        
        if (!validateBarangForm()) {
            return false;
        }
        
        var formData = $(this).serialize();
        var btnSimpan = $('#btnSimpan');
        var originalText = btnSimpan.html();
        
        // Show loading
        showLoading(btnSimpan);
        
        ajaxRequest(
            'ajax/barang_ajax.php',
            formData,
            function(response) {
                // Success callback
                hideLoading(btnSimpan, originalText);
                showSuccess(response.message);
                $('#modalBarang').modal('hide');
                table.ajax.reload();
                resetForm('#formBarang');
            },
            function(message) {
                // Error callback
                hideLoading(btnSimpan, originalText);
                showError(message);
            }
        );
    });
    
    // Handle edit button click
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        
        // Reset form
        resetForm('#formBarang');
        
        // Change modal title and action
        $('#modalBarangLabel').html('<i class="fas fa-edit me-2"></i>Edit Barang');
        $('#action').val('edit');
        
        // Get barang data
        ajaxRequest(
            'ajax/barang_ajax.php',
            { action: 'get', kd_barang: id },
            function(response) {
                // Fill form with data
                $('#kd_barang').val(response.data.kd_barang);
                $('#barcode').val(response.data.barcode);
                $('#nama_barang').val(response.data.nama_barang);
                $('#kd_kategori').val(response.data.kd_kategori);
                $('#kd_merk').val(response.data.kd_merk);
                $('#kd_satuan').val(response.data.kd_satuan);
                $('#stock').val(response.data.stock);
                $('#harga_beli').val(formatNumber(response.data.harga_beli));
                $('#harga_jual').val(formatNumber(response.data.harga_jual));
                $('#keterangan').val(response.data.keterangan);
                
                // Calculate margin
                calculateMargin();
                
                // Show modal
                $('#modalBarang').modal('show');
            },
            function(message) {
                showError(message);
            }
        );
    });
    
    // Handle view button click
    $(document).on('click', '.btn-view', function() {
        var id = $(this).data('id');
        
        // Get barang data for view
        ajaxRequest(
            'ajax/barang_ajax.php',
            { action: 'get', kd_barang: id },
            function(response) {
                var data = response.data;
                var margin = ((data.harga_jual - data.harga_beli) / data.harga_beli * 100).toFixed(2);
                
                var content = `
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr><td><strong>Barcode:</strong></td><td>${data.barcode}</td></tr>
                                <tr><td><strong>Nama Barang:</strong></td><td>${data.nama_barang}</td></tr>
                                <tr><td><strong>Stock:</strong></td><td>${formatNumber(data.stock)}</td></tr>
                                <tr><td><strong>Harga Beli:</strong></td><td>Rp ${formatNumber(data.harga_beli)}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr><td><strong>Harga Jual:</strong></td><td>Rp ${formatNumber(data.harga_jual)}</td></tr>
                                <tr><td><strong>Margin:</strong></td><td>${margin}%</td></tr>
                                <tr><td><strong>Keterangan:</strong></td><td>${data.keterangan || '-'}</td></tr>
                            </table>
                        </div>
                    </div>
                `;
                
                Swal.fire({
                    title: 'Detail Barang',
                    html: content,
                    width: '600px',
                    showConfirmButton: false,
                    showCloseButton: true
                });
            },
            function(message) {
                showError(message);
            }
        );
    });
    
    // Generate barcode button
    $('#generateBarcode').click(function() {
        ajaxRequest(
            'ajax/barang_ajax.php',
            { action: 'generate_barcode' },
            function(response) {
                $('#barcode').val(response.barcode);
                $('#barcode').removeClass('is-invalid').addClass('is-valid');
            },
            function(message) {
                showError(message);
            }
        );
    });
    
    // Reset modal when closed
    $('#modalBarang').on('hidden.bs.modal', function() {
        resetForm('#formBarang');
        $('#modalBarangLabel').html('<i class="fas fa-plus me-2"></i>Tambah Barang');
        $('#action').val('add');
        $('#kd_barang').val('');
        $('#margin').val('');
    });
    
    // Calculate margin when harga changes
    $('#harga_beli, #harga_jual').on('input', function() {
        calculateMargin();
    });
    
    // Handle form validation
    $('#barcode, #nama_barang').on('input', function() {
        var value = $(this).val().trim();
        if (value.length > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    $('#kd_kategori, #kd_merk, #kd_satuan').on('change', function() {
        var value = $(this).val();
        if (value) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    $('#stock').on('input', function() {
        var value = parseInt($(this).val()) || 0;
        if (value >= 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    $('#harga_beli, #harga_jual').on('input', function() {
        var value = parseNumber($(this).val()) || 0;
        if (value > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Auto-focus on modal show
    $('#modalBarang').on('shown.bs.modal', function() {
        $('#barcode').focus();
    });
});

// Calculate margin percentage
function calculateMargin() {
    var hargaBeli = parseNumber($('#harga_beli').val()) || 0;
    var hargaJual = parseNumber($('#harga_jual').val()) || 0;
    
    if (hargaBeli > 0 && hargaJual > 0) {
        var margin = ((hargaJual - hargaBeli) / hargaBeli * 100).toFixed(2);
        $('#margin').val(margin + '%');
        
        // Color coding for margin
        if (margin < 10) {
            $('#margin').removeClass('text-success text-warning').addClass('text-danger');
        } else if (margin < 20) {
            $('#margin').removeClass('text-success text-danger').addClass('text-warning');
        } else {
            $('#margin').removeClass('text-danger text-warning').addClass('text-success');
        }
    } else {
        $('#margin').val('');
        $('#margin').removeClass('text-success text-warning text-danger');
    }
}

// Custom validation function for barang form
function validateBarangForm() {
    var isValid = true;
    
    // Validate required text fields
    $('#barcode, #nama_barang').each(function() {
        if ($(this).val().trim().length === 0) {
            $(this).addClass('is-invalid');
            isValid = false;
        }
    });
    
    // Validate select fields
    $('#kd_kategori, #kd_merk, #kd_satuan').each(function() {
        if (!$(this).val()) {
            $(this).addClass('is-invalid');
            isValid = false;
        }
    });
    
    // Validate stock
    var stock = parseInt($('#stock').val()) || -1;
    if (stock < 0) {
        $('#stock').addClass('is-invalid');
        isValid = false;
    }
    
    // Validate harga
    var hargaBeli = parseNumber($('#harga_beli').val()) || 0;
    var hargaJual = parseNumber($('#harga_jual').val()) || 0;
    
    if (hargaBeli <= 0) {
        $('#harga_beli').addClass('is-invalid');
        isValid = false;
    }
    
    if (hargaJual <= 0) {
        $('#harga_jual').addClass('is-invalid');
        isValid = false;
    }
    
    // Check if harga jual >= harga beli
    if (hargaBeli > 0 && hargaJual > 0 && hargaJual < hargaBeli) {
        $('#harga_jual').addClass('is-invalid');
        showError('Harga jual tidak boleh lebih kecil dari harga beli.');
        isValid = false;
    }
    
    return isValid;
}
