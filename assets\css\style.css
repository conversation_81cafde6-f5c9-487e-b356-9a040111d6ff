/* KOPEGBI POS Custom Styles */

/* Body and Layout */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* Tables */
.table {
    background-color: #fff;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

/* DataTables customization */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #0d6efd !important;
    border-color: #0d6efd !important;
    color: white !important;
}

/* Forms */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Dashboard Stats Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card .stats-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Login Page */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 2rem;
    text-align: center;
}

.login-body {
    padding: 2rem;
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
}

/* Loading Spinner */
.loading {
    display: none;
}

.loading.show {
    display: inline-block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* Print styles */
@media print {
    .navbar, .btn, .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter, .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        display: none !important;
    }
    
    .container-fluid {
        margin-top: 0 !important;
    }
}

/* Barcode Scanner */
.scanner-container {
    position: relative;
    width: 100%;
    height: 300px;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
}

.scanner-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 100px;
    border: 2px solid #00ff00;
    border-radius: 8px;
    z-index: 10;
}

/* Receipt styles */
.receipt {
    width: 80mm;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.2;
}

.receipt-header {
    text-align: center;
    border-bottom: 1px dashed #000;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
}

.receipt-total {
    border-top: 1px dashed #000;
    padding-top: 5px;
    margin-top: 10px;
    font-weight: bold;
}
