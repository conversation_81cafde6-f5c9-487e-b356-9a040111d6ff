<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is logged in
requireLogin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            $nama_pelanggan = trim($_POST['nama_pelanggan'] ?? '');
            
            if (empty($nama_pelanggan)) {
                $response['message'] = 'Nama pelanggan harus diisi.';
                break;
            }
            
            // Check if pelanggan already exists
            $check_query = "SELECT kd_pelanggan FROM pelanggan WHERE nama_pelanggan = ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("s", $nama_pelanggan);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama pelanggan sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Insert new pelanggan
            $insert_query = "INSERT INTO pelanggan (nama_pelanggan) VALUES (?)";
            $insert_stmt = $connection->prepare($insert_query);
            $insert_stmt->bind_param("s", $nama_pelanggan);
            
            if ($insert_stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'Pelanggan berhasil ditambahkan.';
            } else {
                $response['message'] = 'Gagal menambahkan pelanggan.';
            }
            $insert_stmt->close();
            break;
            
        case 'edit':
            $kd_pelanggan = (int)($_POST['kd_pelanggan'] ?? 0);
            $nama_pelanggan = trim($_POST['nama_pelanggan'] ?? '');
            
            if (empty($nama_pelanggan)) {
                $response['message'] = 'Nama pelanggan harus diisi.';
                break;
            }
            
            if ($kd_pelanggan <= 0) {
                $response['message'] = 'ID pelanggan tidak valid.';
                break;
            }
            
            // Check if pelanggan name already exists (except current record)
            $check_query = "SELECT kd_pelanggan FROM pelanggan WHERE nama_pelanggan = ? AND kd_pelanggan != ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("si", $nama_pelanggan, $kd_pelanggan);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama pelanggan sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Update pelanggan
            $update_query = "UPDATE pelanggan SET nama_pelanggan = ? WHERE kd_pelanggan = ?";
            $update_stmt = $connection->prepare($update_query);
            $update_stmt->bind_param("si", $nama_pelanggan, $kd_pelanggan);
            
            if ($update_stmt->execute()) {
                if ($update_stmt->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Pelanggan berhasil diperbarui.';
                } else {
                    $response['message'] = 'Tidak ada perubahan data.';
                }
            } else {
                $response['message'] = 'Gagal memperbarui pelanggan.';
            }
            $update_stmt->close();
            break;
            
        case 'get':
            $kd_pelanggan = (int)($_POST['kd_pelanggan'] ?? 0);
            
            if ($kd_pelanggan <= 0) {
                $response['message'] = 'ID pelanggan tidak valid.';
                break;
            }
            
            $query = "SELECT * FROM pelanggan WHERE kd_pelanggan = ?";
            $stmt = $connection->prepare($query);
            $stmt->bind_param("i", $kd_pelanggan);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $response['success'] = true;
                $response['data'] = $result->fetch_assoc();
            } else {
                $response['message'] = 'Pelanggan tidak ditemukan.';
            }
            $stmt->close();
            break;
            
        default:
            $response['message'] = 'Aksi tidak valid.';
            break;
    }
}

// Handle DataTables server-side processing
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['draw'])) {
    $draw = (int)$_GET['draw'];
    $start = (int)$_GET['start'];
    $length = (int)$_GET['length'];
    $search = $_GET['search']['value'] ?? '';
    
    // Base query
    $base_query = "FROM pelanggan";
    $where_clause = "";
    $params = [];
    $param_types = "";
    
    // Search functionality
    if (!empty($search)) {
        $where_clause = " WHERE nama_pelanggan LIKE ?";
        $params[] = "%$search%";
        $param_types .= "s";
    }
    
    // Count total records
    $count_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    $count_stmt = $connection->prepare($count_query);
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
    
    // Get data with pagination
    $data_query = "SELECT kd_pelanggan, nama_pelanggan " . $base_query . $where_clause . " ORDER BY nama_pelanggan ASC LIMIT ?, ?";
    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";
    
    $data_stmt = $connection->prepare($data_query);
    $data_stmt->bind_param($param_types, ...$params);
    $data_stmt->execute();
    $result = $data_stmt->get_result();
    
    $data = [];
    $no = $start + 1;
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            $no++,
            htmlspecialchars($row['nama_pelanggan']),
            '<div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-warning btn-edit" data-id="' . $row['kd_pelanggan'] . '" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <a href="?action=delete&id=' . $row['kd_pelanggan'] . '" class="btn btn-sm btn-danger btn-delete" title="Hapus" data-message="Apakah Anda yakin ingin menghapus pelanggan ' . htmlspecialchars($row['nama_pelanggan']) . '?">
                    <i class="fas fa-trash"></i>
                </a>
            </div>'
        ];
    }
    $data_stmt->close();
    
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ];
}

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
?>
