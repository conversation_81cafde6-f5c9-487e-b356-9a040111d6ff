<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is logged in
requireLogin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
        case 'cari_barang':
            $search = trim($_POST['search'] ?? '');

            // Allow empty search to show all available items
            if (empty($search)) {
                $search = '';
            }

            // Search barang by barcode or nama_barang
            if (empty($search)) {
                // Show all items if no search term
                $query = "SELECT b.kd_barang, b.barcode, b.nama_barang, b.stock, b.harga_beli, b.harga_jual, s.nama_satuan
                          FROM barang b
                          JOIN satuan s ON b.kd_satuan = s.kd_satuan
                          WHERE b.stock > 0
                          ORDER BY b.nama_barang ASC
                          LIMIT 50";
                $stmt = $connection->prepare($query);
                $stmt->execute();
            } else {
                // Search with term
                $query = "SELECT b.kd_barang, b.barcode, b.nama_barang, b.stock, b.harga_beli, b.harga_jual, s.nama_satuan
                          FROM barang b
                          JOIN satuan s ON b.kd_satuan = s.kd_satuan
                          WHERE (b.barcode LIKE ? OR b.nama_barang LIKE ?) AND b.stock > 0
                          ORDER BY b.nama_barang ASC
                          LIMIT 50";
                $stmt = $connection->prepare($query);
                $search_param = "%$search%";
                $stmt->bind_param("ss", $search_param, $search_param);
                $stmt->execute();
            }
            $result = $stmt->get_result();

            $barang_list = [];
            while ($row = $result->fetch_assoc()) {
                $barang_list[] = $row;
            }

            $response['success'] = true;
            $response['data'] = $barang_list;
            $stmt->close();
            break;

        case 'get_barang_by_barcode':
            $barcode = trim($_POST['barcode'] ?? '');

            if (empty($barcode)) {
                $response['message'] = 'Barcode harus diisi.';
                break;
            }

            // Get barang by exact barcode match
            $query = "SELECT b.kd_barang, b.barcode, b.nama_barang, b.stock, b.harga_beli, b.harga_jual, s.nama_satuan
                      FROM barang b
                      JOIN satuan s ON b.kd_satuan = s.kd_satuan
                      WHERE b.barcode = ? AND b.stock > 0";

            $stmt = $connection->prepare($query);
            $stmt->bind_param("s", $barcode);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $response['success'] = true;
                $response['data'] = $result->fetch_assoc();
            } else {
                $response['message'] = 'Barang dengan barcode tersebut tidak ditemukan atau stock habis.';
            }
            $stmt->close();
            break;

        case 'simpan_transaksi':
            $no_transaksi = trim($_POST['no_transaksi'] ?? '');
            $tgl_penjualan = trim($_POST['tgl_penjualan'] ?? '');
            $tgl_jt = trim($_POST['tgl_jt'] ?? '');
            $kd_pelanggan = (int)($_POST['kd_pelanggan'] ?? 0);
            $cara_bayar = trim($_POST['cara_bayar'] ?? '');
            $dibayar = (int)str_replace('.', '', $_POST['dibayar'] ?? '0');
            $kembalian = (int)str_replace('.', '', $_POST['kembalian'] ?? '0');
            $keterangan = trim($_POST['keterangan'] ?? '');
            $items = json_decode($_POST['items'] ?? '[]', true);
            $current_user = getCurrentUser();

            // Validation
            if (empty($no_transaksi)) {
                $response['message'] = 'No. transaksi harus diisi.';
                break;
            }

            if (empty($tgl_penjualan)) {
                $response['message'] = 'Tanggal penjualan harus diisi.';
                break;
            }

            // Set default tgl_jt to tgl_penjualan if empty
            if (empty($tgl_jt)) {
                $tgl_jt = $tgl_penjualan;
            }

            // Validate tgl_jt is not before tgl_penjualan
            if (strtotime($tgl_jt) < strtotime($tgl_penjualan)) {
                $response['message'] = 'Tanggal jatuh tempo tidak boleh lebih awal dari tanggal penjualan.';
                break;
            }

            if (empty($cara_bayar)) {
                $response['message'] = 'Cara bayar harus dipilih.';
                break;
            }

            if (empty($items) || !is_array($items)) {
                $response['message'] = 'Tidak ada item yang akan disimpan.';
                break;
            }

            if ($dibayar <= 0) {
                $response['message'] = 'Jumlah dibayar harus lebih dari 0.';
                break;
            }

            // Start transaction
            $connection->autocommit(false);

            try {
                // Check if transaction number already exists
                $check_query = "SELECT kd_penjualan FROM penjualan WHERE no_transaksi = ?";
                $check_stmt = $connection->prepare($check_query);
                $check_stmt->bind_param("s", $no_transaksi);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();

                if ($check_result->num_rows > 0) {
                    throw new Exception('No. transaksi sudah ada.');
                }
                $check_stmt->close();

                // Insert penjualan header
                $insert_header = "INSERT INTO penjualan (no_transaksi, tgl_penjualan, tgl_jt, kd_pelanggan, cara_bayar, kd_user, keterangan, dibayar, kembalian) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt_header = $connection->prepare($insert_header);

                // Handle null pelanggan
                if ($kd_pelanggan <= 0) {
                    $kd_pelanggan = null;
                }

                $stmt_header->bind_param("sssisisii", $no_transaksi, $tgl_penjualan, $tgl_jt, $kd_pelanggan, $cara_bayar, $current_user['kd_user'], $keterangan, $dibayar, $kembalian);

                if (!$stmt_header->execute()) {
                    throw new Exception('Gagal menyimpan header transaksi.');
                }

                $kd_penjualan = $connection->insert_id;
                $stmt_header->close();

                // Insert penjualan items and update stock
                $insert_item = "INSERT INTO penjualan_item (kd_penjualan, no_transaksi, kd_barang, harga_beli, harga_jual, jumlah, disc) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt_item = $connection->prepare($insert_item);

                $update_stock = "UPDATE barang SET stock = stock - ? WHERE kd_barang = ?";
                $stmt_stock = $connection->prepare($update_stock);

                foreach ($items as $item) {
                    $kd_barang = (int)$item['kd_barang'];
                    $harga_beli = (int)$item['harga_beli'];
                    $harga_jual = (int)$item['harga_jual'];
                    $jumlah = (int)$item['jumlah'];
                    $disc = (int)$item['disc'];

                    // Validate item
                    if ($kd_barang <= 0 || $jumlah <= 0) {
                        throw new Exception('Data item tidak valid.');
                    }

                    // Check stock availability
                    $stock_query = "SELECT stock FROM barang WHERE kd_barang = ?";
                    $stock_stmt = $connection->prepare($stock_query);
                    $stock_stmt->bind_param("i", $kd_barang);
                    $stock_stmt->execute();
                    $stock_result = $stock_stmt->get_result();

                    if ($stock_result->num_rows == 0) {
                        throw new Exception('Barang tidak ditemukan.');
                    }

                    $current_stock = $stock_result->fetch_assoc()['stock'];
                    $stock_stmt->close();

                    if ($current_stock < $jumlah) {
                        throw new Exception('Stock barang tidak mencukupi.');
                    }

                    // Insert item
                    $stmt_item->bind_param("isiiii", $kd_penjualan, $no_transaksi, $kd_barang, $harga_beli, $harga_jual, $jumlah, $disc);
                    if (!$stmt_item->execute()) {
                        throw new Exception('Gagal menyimpan item transaksi.');
                    }

                    // Update stock
                    $stmt_stock->bind_param("ii", $jumlah, $kd_barang);
                    if (!$stmt_stock->execute()) {
                        throw new Exception('Gagal mengupdate stock barang.');
                    }
                }

                $stmt_item->close();
                $stmt_stock->close();

                // Commit transaction
                $connection->commit();

                $response['success'] = true;
                $response['message'] = 'Transaksi berhasil disimpan.';
                $response['kd_penjualan'] = $kd_penjualan;
                $response['no_transaksi'] = $no_transaksi;

            } catch (Exception $e) {
                // Rollback transaction
                $connection->rollback();
                $response['message'] = $e->getMessage();
            }

            // Restore autocommit
            $connection->autocommit(true);
            break;

        case 'generate_transaction_number':
            $response['success'] = true;
            $response['no_transaksi'] = generatePenjualanTransactionNumber();
            break;

        default:
            $response['message'] = 'Aksi tidak valid: ' . $action;
            break;
        }
    } catch (Exception $e) {
        $response['message'] = 'Terjadi kesalahan: ' . $e->getMessage();
    }
}

// No GET requests needed for this module

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
?>
