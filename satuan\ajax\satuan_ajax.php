<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is logged in
requireLogin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            $nama_satuan = trim($_POST['nama_satuan'] ?? '');
            
            if (empty($nama_satuan)) {
                $response['message'] = 'Nama satuan harus diisi.';
                break;
            }
            
            // Check if satuan already exists
            $check_query = "SELECT kd_satuan FROM satuan WHERE nama_satuan = ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("s", $nama_satuan);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama satuan sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Insert new satuan
            $insert_query = "INSERT INTO satuan (nama_satuan) VALUES (?)";
            $insert_stmt = $connection->prepare($insert_query);
            $insert_stmt->bind_param("s", $nama_satuan);
            
            if ($insert_stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'Satuan berhasil ditambahkan.';
            } else {
                $response['message'] = 'Gagal menambahkan satuan.';
            }
            $insert_stmt->close();
            break;
            
        case 'edit':
            $kd_satuan = (int)($_POST['kd_satuan'] ?? 0);
            $nama_satuan = trim($_POST['nama_satuan'] ?? '');
            
            if (empty($nama_satuan)) {
                $response['message'] = 'Nama satuan harus diisi.';
                break;
            }
            
            if ($kd_satuan <= 0) {
                $response['message'] = 'ID satuan tidak valid.';
                break;
            }
            
            // Check if satuan name already exists (except current record)
            $check_query = "SELECT kd_satuan FROM satuan WHERE nama_satuan = ? AND kd_satuan != ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("si", $nama_satuan, $kd_satuan);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Nama satuan sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Update satuan
            $update_query = "UPDATE satuan SET nama_satuan = ? WHERE kd_satuan = ?";
            $update_stmt = $connection->prepare($update_query);
            $update_stmt->bind_param("si", $nama_satuan, $kd_satuan);
            
            if ($update_stmt->execute()) {
                if ($update_stmt->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Satuan berhasil diperbarui.';
                } else {
                    $response['message'] = 'Tidak ada perubahan data.';
                }
            } else {
                $response['message'] = 'Gagal memperbarui satuan.';
            }
            $update_stmt->close();
            break;
            
        case 'get':
            $kd_satuan = (int)($_POST['kd_satuan'] ?? 0);
            
            if ($kd_satuan <= 0) {
                $response['message'] = 'ID satuan tidak valid.';
                break;
            }
            
            $query = "SELECT * FROM satuan WHERE kd_satuan = ?";
            $stmt = $connection->prepare($query);
            $stmt->bind_param("i", $kd_satuan);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $response['success'] = true;
                $response['data'] = $result->fetch_assoc();
            } else {
                $response['message'] = 'Satuan tidak ditemukan.';
            }
            $stmt->close();
            break;
            
        default:
            $response['message'] = 'Aksi tidak valid.';
            break;
    }
}

// Handle DataTables server-side processing
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['draw'])) {
    $draw = (int)$_GET['draw'];
    $start = (int)$_GET['start'];
    $length = (int)$_GET['length'];
    $search = $_GET['search']['value'] ?? '';
    
    // Base query
    $base_query = "FROM satuan";
    $where_clause = "";
    $params = [];
    $param_types = "";
    
    // Search functionality
    if (!empty($search)) {
        $where_clause = " WHERE nama_satuan LIKE ?";
        $params[] = "%$search%";
        $param_types .= "s";
    }
    
    // Count total records
    $count_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    $count_stmt = $connection->prepare($count_query);
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
    
    // Get data with pagination
    $data_query = "SELECT kd_satuan, nama_satuan " . $base_query . $where_clause . " ORDER BY nama_satuan ASC LIMIT ?, ?";
    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";
    
    $data_stmt = $connection->prepare($data_query);
    $data_stmt->bind_param($param_types, ...$params);
    $data_stmt->execute();
    $result = $data_stmt->get_result();
    
    $data = [];
    $no = $start + 1;
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            $no++,
            htmlspecialchars($row['nama_satuan']),
            '<div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-warning btn-edit" data-id="' . $row['kd_satuan'] . '" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <a href="?action=delete&id=' . $row['kd_satuan'] . '" class="btn btn-sm btn-danger btn-delete" title="Hapus" data-message="Apakah Anda yakin ingin menghapus satuan ' . htmlspecialchars($row['nama_satuan']) . '?">
                    <i class="fas fa-trash"></i>
                </a>
            </div>'
        ];
    }
    $data_stmt->close();
    
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ];
}

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
?>
