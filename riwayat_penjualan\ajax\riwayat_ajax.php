<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is logged in
requireLogin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'get_transactions':
                // Get transactions with filters
                $tanggal_dari = $_POST['tanggal_dari'] ?? '';
                $tanggal_sampai = $_POST['tanggal_sampai'] ?? '';
                $pelanggan = $_POST['pelanggan'] ?? '';
                $kasir = $_POST['kasir'] ?? '';
                $cara_bayar = $_POST['cara_bayar'] ?? '';
                $no_transaksi = $_POST['no_transaksi'] ?? '';
                $status = $_POST['status'] ?? '';

                // Build query sesuai struktur tabel penjualan
                $query = "SELECT p.kd_penjualan, p.no_transaksi, p.tgl_penjualan, p.tgl_jt,
                                p.dibayar, p.kembalian, p.cara_bayar, p.keterangan,
                                pl.nama_pelanggan, u.nama_user
                         FROM penjualan p
                         LEFT JOIN pelanggan pl ON p.kd_pelanggan = pl.kd_pelanggan
                         LEFT JOIN user u ON p.kd_user = u.kd_user
                         WHERE 1=1";

                $params = [];
                $types = "";

                // Add filters
                if (!empty($tanggal_dari)) {
                    $query .= " AND DATE(p.tgl_penjualan) >= ?";
                    $params[] = $tanggal_dari;
                    $types .= "s";
                }

                if (!empty($tanggal_sampai)) {
                    $query .= " AND DATE(p.tgl_penjualan) <= ?";
                    $params[] = $tanggal_sampai;
                    $types .= "s";
                }

                if (!empty($pelanggan)) {
                    $query .= " AND p.kd_pelanggan = ?";
                    $params[] = $pelanggan;
                    $types .= "i";
                }

                if (!empty($kasir)) {
                    $query .= " AND p.kd_user = ?";
                    $params[] = $kasir;
                    $types .= "i";
                }

                if (!empty($cara_bayar)) {
                    $query .= " AND p.cara_bayar = ?";
                    $params[] = $cara_bayar;
                    $types .= "s";
                }

                if (!empty($no_transaksi)) {
                    $query .= " AND p.no_transaksi LIKE ?";
                    $params[] = "%$no_transaksi%";
                    $types .= "s";
                }

                $query .= " ORDER BY p.tgl_penjualan DESC, p.no_transaksi DESC";

                // Execute query
                if (!empty($params)) {
                    $stmt = $connection->prepare($query);
                    $stmt->bind_param($types, ...$params);
                    $stmt->execute();
                    $result = $stmt->get_result();
                } else {
                    $result = $connection->query($query);
                }

                $transactions = [];
                while ($row = $result->fetch_assoc()) {
                    $transactions[] = [
                        'kd_penjualan' => $row['kd_penjualan'],
                        'no_transaksi' => $row['no_transaksi'],
                        'tgl_penjualan' => $row['tgl_penjualan'],
                        'nama_pelanggan' => $row['nama_pelanggan'] ?: 'Umum',
                        'dibayar' => $row['dibayar'],
                        'kembalian' => $row['kembalian'],
                        'cara_bayar' => $row['cara_bayar'],
                        'nama_user' => $row['nama_user'],
                        'tgl_jt' => $row['tgl_jt'],
                        'keterangan' => $row['keterangan']
                    ];
                }

                if (isset($stmt)) {
                    $stmt->close();
                }

                $response['success'] = true;
                $response['data'] = $transactions;
                break;

            case 'get_detail':
                $kd_penjualan = (int)($_POST['kd_penjualan'] ?? 0);

                if ($kd_penjualan <= 0) {
                    $response['message'] = 'ID transaksi tidak valid.';
                    break;
                }

                // Get transaction header
                $header_query = "SELECT p.*, pl.nama_pelanggan, u.nama_user
                                FROM penjualan p
                                LEFT JOIN pelanggan pl ON p.kd_pelanggan = pl.kd_pelanggan
                                LEFT JOIN user u ON p.kd_user = u.kd_user
                                WHERE p.kd_penjualan = ?";
                $stmt = $connection->prepare($header_query);
                $stmt->bind_param("i", $kd_penjualan);
                $stmt->execute();
                $header = $stmt->get_result()->fetch_assoc();
                $stmt->close();

                if (!$header) {
                    $response['message'] = 'Transaksi tidak ditemukan.';
                    break;
                }

                // Get transaction items
                $items_query = "SELECT pi.*, b.nama_barang, b.barcode, s.nama_satuan
                               FROM penjualan_item pi
                               JOIN barang b ON pi.kd_barang = b.kd_barang
                               LEFT JOIN satuan s ON b.kd_satuan = s.kd_satuan
                               WHERE pi.kd_penjualan = ?
                               ORDER BY pi.kd_barang";
                $stmt = $connection->prepare($items_query);
                $stmt->bind_param("i", $kd_penjualan);
                $stmt->execute();
                $items_result = $stmt->get_result();

                $items = [];
                while ($item = $items_result->fetch_assoc()) {
                    $items[] = $item;
                }
                $stmt->close();

                $response['success'] = true;
                $response['data'] = [
                    'header' => $header,
                    'items' => $items
                ];
                break;

            default:
                $response['message'] = 'Aksi tidak valid: ' . $action;
                break;
        }
    } catch (Exception $e) {
        $response['message'] = 'Terjadi kesalahan: ' . $e->getMessage();
    }
}

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
?>
