<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is logged in
requireLogin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'get_summary':
                // Get summary data
                $today = date('Y-m-d');
                $week_start = date('Y-m-d', strtotime('monday this week'));
                $month_start = date('Y-m-01');

                // Today's transactions with correct total from penjualan_item
                $today_query = "SELECT COUNT(DISTINCT p.kd_penjualan) as count,
                               COALESCE(SUM((pi.harga_jual * pi.jumlah) - ((pi.harga_jual * pi.jumlah * pi.disc) / 100)), 0) as total
                               FROM penjualan p
                               LEFT JOIN penjualan_item pi ON p.kd_penjualan = pi.kd_penjualan
                               WHERE DATE(p.tgl_penjualan) = ?";
                $stmt = $connection->prepare($today_query);
                $stmt->bind_param("s", $today);
                $stmt->execute();
                $today_result = $stmt->get_result()->fetch_assoc();
                $stmt->close();

                // This week's transactions
                $week_query = "SELECT COUNT(DISTINCT p.kd_penjualan) as count,
                              COALESCE(SUM((pi.harga_jual * pi.jumlah) - ((pi.harga_jual * pi.jumlah * pi.disc) / 100)), 0) as total
                              FROM penjualan p
                              LEFT JOIN penjualan_item pi ON p.kd_penjualan = pi.kd_penjualan
                              WHERE DATE(p.tgl_penjualan) >= ?";
                $stmt = $connection->prepare($week_query);
                $stmt->bind_param("s", $week_start);
                $stmt->execute();
                $week_result = $stmt->get_result()->fetch_assoc();
                $stmt->close();

                // This month's transactions
                $month_query = "SELECT COUNT(DISTINCT p.kd_penjualan) as count,
                               COALESCE(SUM((pi.harga_jual * pi.jumlah) - ((pi.harga_jual * pi.jumlah * pi.disc) / 100)), 0) as total
                               FROM penjualan p
                               LEFT JOIN penjualan_item pi ON p.kd_penjualan = pi.kd_penjualan
                               WHERE DATE(p.tgl_penjualan) >= ?";
                $stmt = $connection->prepare($month_query);
                $stmt->bind_param("s", $month_start);
                $stmt->execute();
                $month_result = $stmt->get_result()->fetch_assoc();
                $stmt->close();

                // Total transactions
                $total_query = "SELECT COUNT(DISTINCT p.kd_penjualan) as count,
                               COALESCE(SUM((pi.harga_jual * pi.jumlah) - ((pi.harga_jual * pi.jumlah * pi.disc) / 100)), 0) as total
                               FROM penjualan p
                               LEFT JOIN penjualan_item pi ON p.kd_penjualan = pi.kd_penjualan";
                $total_result = $connection->query($total_query)->fetch_assoc();

                $response['success'] = true;
                $response['data'] = [
                    'today' => [
                        'count' => $today_result['count'],
                        'total' => $today_result['total']
                    ],
                    'week' => [
                        'count' => $week_result['count'],
                        'total' => $week_result['total']
                    ],
                    'month' => [
                        'count' => $month_result['count'],
                        'total' => $month_result['total']
                    ],
                    'total' => [
                        'count' => $total_result['count'],
                        'total' => $total_result['total']
                    ]
                ];
                break;

            case 'get_transactions':
                // Get transactions with filters
                $tanggal_dari = $_POST['tanggal_dari'] ?? '';
                $tanggal_sampai = $_POST['tanggal_sampai'] ?? '';
                $pelanggan = $_POST['pelanggan'] ?? '';
                $kasir = $_POST['kasir'] ?? '';
                $cara_bayar = $_POST['cara_bayar'] ?? '';
                $no_transaksi = $_POST['no_transaksi'] ?? '';
                $status = $_POST['status'] ?? '';

                // Build query with correct total calculation from penjualan_item
                $query = "SELECT p.kd_penjualan, p.no_transaksi, p.tgl_penjualan, p.tgl_jt,
                                p.dibayar, p.kembalian, p.cara_bayar, p.keterangan,
                                pl.nama_pelanggan, u.nama_user,
                                COALESCE(totals.total_transaksi, 0) as total_transaksi
                         FROM penjualan p
                         LEFT JOIN pelanggan pl ON p.kd_pelanggan = pl.kd_pelanggan
                         LEFT JOIN user u ON p.kd_user = u.kd_user
                         LEFT JOIN (
                             SELECT kd_penjualan,
                                    SUM((harga_jual * jumlah) - ((harga_jual * jumlah * disc) / 100)) as total_transaksi
                             FROM penjualan_item
                             GROUP BY kd_penjualan
                         ) totals ON p.kd_penjualan = totals.kd_penjualan
                         WHERE 1=1";

                $params = [];
                $types = "";

                // Add filters
                if (!empty($tanggal_dari)) {
                    $query .= " AND DATE(p.tgl_penjualan) >= ?";
                    $params[] = $tanggal_dari;
                    $types .= "s";
                }

                if (!empty($tanggal_sampai)) {
                    $query .= " AND DATE(p.tgl_penjualan) <= ?";
                    $params[] = $tanggal_sampai;
                    $types .= "s";
                }

                if (!empty($pelanggan)) {
                    $query .= " AND p.kd_pelanggan = ?";
                    $params[] = $pelanggan;
                    $types .= "i";
                }

                if (!empty($kasir)) {
                    $query .= " AND p.kd_user = ?";
                    $params[] = $kasir;
                    $types .= "i";
                }

                if (!empty($cara_bayar)) {
                    $query .= " AND p.cara_bayar = ?";
                    $params[] = $cara_bayar;
                    $types .= "s";
                }

                if (!empty($no_transaksi)) {
                    $query .= " AND p.no_transaksi LIKE ?";
                    $params[] = "%$no_transaksi%";
                    $types .= "s";
                }

                if (!empty($status)) {
                    if ($status == 'lunas') {
                        $query .= " AND (p.cara_bayar = 'Cash' OR DATE(p.tgl_jt) <= CURDATE())";
                    } elseif ($status == 'belum_lunas') {
                        $query .= " AND p.cara_bayar != 'Cash' AND DATE(p.tgl_jt) > CURDATE()";
                    }
                }

                $query .= " ORDER BY p.tgl_penjualan DESC, p.kd_penjualan DESC";

                // Execute query
                if (!empty($params)) {
                    $stmt = $connection->prepare($query);
                    $stmt->bind_param($types, ...$params);
                    $stmt->execute();
                    $result = $stmt->get_result();
                } else {
                    $result = $connection->query($query);
                }

                $transactions = [];
                while ($row = $result->fetch_assoc()) {
                    // Determine status
                    $status_label = 'Lunas';
                    $status_class = 'success';

                    if ($row['cara_bayar'] != 'Cash') {
                        if (strtotime($row['tgl_jt']) > time()) {
                            $status_label = 'Belum Lunas';
                            $status_class = 'warning';
                        }
                    }

                    $transactions[] = [
                        'kd_penjualan' => $row['kd_penjualan'],
                        'no_transaksi' => $row['no_transaksi'],
                        'tgl_penjualan' => $row['tgl_penjualan'],
                        'jam' => date('H:i', strtotime($row['tgl_penjualan'])),
                        'nama_pelanggan' => $row['nama_pelanggan'] ?: 'Umum',
                        'total_transaksi' => $row['total_transaksi'],
                        'cara_bayar' => $row['cara_bayar'],
                        'nama_user' => $row['nama_user'],
                        'status_label' => $status_label,
                        'status_class' => $status_class,
                        'tgl_jt' => $row['tgl_jt']
                    ];
                }

                if (isset($stmt)) {
                    $stmt->close();
                }

                $response['success'] = true;
                $response['data'] = $transactions;
                break;

            case 'get_detail':
                $kd_penjualan = (int)($_POST['kd_penjualan'] ?? 0);

                if ($kd_penjualan <= 0) {
                    $response['message'] = 'ID transaksi tidak valid.';
                    break;
                }

                // Get transaction header
                $header_query = "SELECT p.*, pl.nama_pelanggan, u.nama_user
                                FROM penjualan p
                                LEFT JOIN pelanggan pl ON p.kd_pelanggan = pl.kd_pelanggan
                                LEFT JOIN user u ON p.kd_user = u.kd_user
                                WHERE p.kd_penjualan = ?";
                $stmt = $connection->prepare($header_query);
                $stmt->bind_param("i", $kd_penjualan);
                $stmt->execute();
                $header = $stmt->get_result()->fetch_assoc();
                $stmt->close();

                if (!$header) {
                    $response['message'] = 'Transaksi tidak ditemukan.';
                    break;
                }

                // Get transaction items
                $items_query = "SELECT pi.*, b.nama_barang, b.barcode, s.nama_satuan
                               FROM penjualan_item pi
                               JOIN barang b ON pi.kd_barang = b.kd_barang
                               LEFT JOIN satuan s ON b.kd_satuan = s.kd_satuan
                               WHERE pi.kd_penjualan = ?
                               ORDER BY pi.kd_barang";
                $stmt = $connection->prepare($items_query);
                $stmt->bind_param("i", $kd_penjualan);
                $stmt->execute();
                $items_result = $stmt->get_result();

                $items = [];
                while ($item = $items_result->fetch_assoc()) {
                    $items[] = $item;
                }
                $stmt->close();

                $response['success'] = true;
                $response['data'] = [
                    'header' => $header,
                    'items' => $items
                ];
                break;

            default:
                $response['message'] = 'Aksi tidak valid: ' . $action;
                break;
        }
    } catch (Exception $e) {
        $response['message'] = 'Terjadi kesalahan: ' . $e->getMessage();
    }
}

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
?>
