/**
 * Kategori Management JavaScript
 */

$(document).ready(function() {
    // Initialize DataTable
    var table = initDataTable('#tableKategori', {
        ajax: {
            url: 'ajax/kategori_ajax.php',
            type: 'GET'
        },
        columns: [
            { data: 0, orderable: false },
            { data: 1 },
            { data: 2, orderable: false, searchable: false }
        ],
        order: [[1, 'asc']]
    });

    // Handle form submission
    $('#formKategori').submit(function(e) {
        e.preventDefault();

        if (!validateForm('#formKategori')) {
            return false;
        }

        var formData = $(this).serialize();
        var btnSimpan = $('#btnSimpan');
        var originalText = btnSimpan.html();

        // Show loading
        showLoading(btnSimpan);

        ajaxRequest(
            'ajax/kategori_ajax.php',
            formData,
            function(response) {
                // Success callback
                hideLoading(btnSimpan, originalText);
                showSuccess(response.message);
                $('#modalKategori').modal('hide');
                table.ajax.reload();
                resetForm('#formKategori');
            },
            function(message) {
                // Error callback
                hideLoading(btnSimpan, originalText);
                showError(message);
            }
        );
    });

    // Handle edit button click
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');

        // Reset form
        resetForm('#formKategori');

        // Change modal title and action
        $('#modalKategoriLabel').html('<i class="fas fa-edit me-2"></i>Edit Kategori');
        $('#action').val('edit');

        // Get kategori data
        ajaxRequest(
            'ajax/kategori_ajax.php',
            { action: 'get', kd_kategori: id },
            function(response) {
                // Fill form with data
                $('#kd_kategori').val(response.data.kd_kategori);
                $('#nama_kategori').val(response.data.nama_kategori);

                // Show modal
                $('#modalKategori').modal('show');
            },
            function(message) {
                showError(message);
            }
        );
    });

    // Reset modal when closed
    $('#modalKategori').on('hidden.bs.modal', function() {
        resetForm('#formKategori');
        $('#modalKategoriLabel').html('<i class="fas fa-plus me-2"></i>Tambah Kategori');
        $('#action').val('add');
        $('#kd_kategori').val('');
    });

    // Handle form validation
    $('#nama_kategori').on('input', function() {
        var value = $(this).val().trim();
        if (value.length > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });

    // Auto-focus on modal show
    $('#modalKategori').on('shown.bs.modal', function() {
        $('#nama_kategori').focus();
    });
});

// Additional validation function specific to kategori
function validateKategoriForm() {
    var isValid = true;
    var namaKategori = $('#nama_kategori').val().trim();

    // Validate nama kategori
    if (namaKategori.length === 0) {
        $('#nama_kategori').addClass('is-invalid');
        isValid = false;
    } else if (namaKategori.length > 50) {
        $('#nama_kategori').addClass('is-invalid');
        showError('Nama kategori maksimal 50 karakter.');
        isValid = false;
    } else {
        $('#nama_kategori').removeClass('is-invalid').addClass('is-valid');
    }

    return isValid;
}
