/**
 * User Management JavaScript
 */

$(document).ready(function() {
    // Initialize DataTable
    var table = initDataTable('#tableUser', {
        ajax: {
            url: 'ajax/user_ajax.php',
            type: 'GET'
        },
        columns: [
            { data: 0, orderable: false },
            { data: 1 },
            { data: 2 },
            { data: 3 },
            { data: 4 },
            { data: 5, orderable: false },
            { data: 6, orderable: false, searchable: false }
        ],
        order: [[2, 'asc']]
    });
    
    // Handle form submission
    $('#formUser').submit(function(e) {
        e.preventDefault();
        
        if (!validateUserForm()) {
            return false;
        }
        
        var formData = $(this).serialize();
        var btnSimpan = $('#btnSimpan');
        var originalText = btnSimpan.html();
        
        // Show loading
        showLoading(btnSimpan);
        
        ajaxRequest(
            'ajax/user_ajax.php',
            formData,
            function(response) {
                // Success callback
                hideLoading(btnSimpan, originalText);
                showSuccess(response.message);
                $('#modalUser').modal('hide');
                table.ajax.reload();
                resetForm('#formUser');
            },
            function(message) {
                // Error callback
                hideLoading(btnSimpan, originalText);
                showError(message);
            }
        );
    });
    
    // Handle edit button click
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        
        // Reset form
        resetForm('#formUser');
        
        // Change modal title and action
        $('#modalUserLabel').html('<i class="fas fa-edit me-2"></i>Edit User');
        $('#action').val('edit');
        
        // Show password help text and make password optional
        $('#password-required').hide();
        $('#password-help').show();
        $('#password').removeAttr('required');
        
        // Get user data
        ajaxRequest(
            'ajax/user_ajax.php',
            { action: 'get', kd_user: id },
            function(response) {
                // Fill form with data
                $('#kd_user').val(response.data.kd_user);
                $('#nik').val(response.data.nik);
                $('#nama_user').val(response.data.nama_user);
                $('#jk').val(response.data.jk);
                $('#username').val(response.data.username);
                $('#level').val(response.data.level);
                
                // Show modal
                $('#modalUser').modal('show');
            },
            function(message) {
                showError(message);
            }
        );
    });
    
    // Reset modal when closed
    $('#modalUser').on('hidden.bs.modal', function() {
        resetForm('#formUser');
        $('#modalUserLabel').html('<i class="fas fa-plus me-2"></i>Tambah User');
        $('#action').val('add');
        $('#kd_user').val('');
        
        // Reset password field requirements
        $('#password-required').show();
        $('#password-help').hide();
        $('#password').attr('required', 'required');
    });
    
    // Toggle password visibility
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Handle form validation
    $('#nik, #nama_user, #username').on('input', function() {
        var value = $(this).val().trim();
        if (value.length > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    $('#jk, #level').on('change', function() {
        var value = $(this).val();
        if (value) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    $('#password').on('input', function() {
        var value = $(this).val().trim();
        var isEdit = $('#action').val() === 'edit';
        
        if (isEdit && value.length === 0) {
            // For edit, password is optional
            $(this).removeClass('is-invalid is-valid');
        } else if (value.length > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Username validation (alphanumeric only)
    $('#username').on('input', function() {
        var value = $(this).val().trim();
        var usernameRegex = /^[a-zA-Z0-9_]+$/;
        
        if (value.length > 0 && usernameRegex.test(value)) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else if (value.length > 0) {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // Auto-focus on modal show
    $('#modalUser').on('shown.bs.modal', function() {
        $('#nik').focus();
    });
});

// Custom validation function for user form
function validateUserForm() {
    var isValid = true;
    var isEdit = $('#action').val() === 'edit';
    
    // Validate required fields
    $('#nik, #nama_user, #username').each(function() {
        if ($(this).val().trim().length === 0) {
            $(this).addClass('is-invalid');
            isValid = false;
        }
    });
    
    // Validate select fields
    $('#jk, #level').each(function() {
        if (!$(this).val()) {
            $(this).addClass('is-invalid');
            isValid = false;
        }
    });
    
    // Validate password (required for add, optional for edit)
    var password = $('#password').val().trim();
    if (!isEdit && password.length === 0) {
        $('#password').addClass('is-invalid');
        isValid = false;
    }
    
    // Validate username format
    var username = $('#username').val().trim();
    var usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (username.length > 0 && !usernameRegex.test(username)) {
        $('#username').addClass('is-invalid');
        showError('Username hanya boleh mengandung huruf, angka, dan underscore.');
        isValid = false;
    }
    
    return isValid;
}
