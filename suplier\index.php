<?php
$page_title = 'Master Supplier';
require_once '../config/database.php';
require_once '../includes/header.php';

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $kd_suplier = (int)$_GET['id'];
    $connection = getConnection();
    
    // Check if supplier is used in pembelian or retur_pembelian
    $check_query = "SELECT 
                        (SELECT COUNT(*) FROM pembelian WHERE kd_suplier = ?) +
                        (SELECT COUNT(*) FROM retur_pembelian WHERE kd_suplier = ?) as count";
    $check_stmt = $connection->prepare($check_query);
    $check_stmt->bind_param("ii", $kd_suplier, $kd_suplier);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $count = $check_result->fetch_assoc()['count'];
    
    if ($count > 0) {
        $error_message = "Supplier tidak dapat dihapus karena masih digunakan pada $count transaksi.";
    } else {
        $delete_query = "DELETE FROM suplier WHERE kd_suplier = ?";
        $delete_stmt = $connection->prepare($delete_query);
        $delete_stmt->bind_param("i", $kd_suplier);
        
        if ($delete_stmt->execute()) {
            $success_message = "Supplier berhasil dihapus.";
        } else {
            $error_message = "Gagal menghapus supplier.";
        }
        $delete_stmt->close();
    }
    
    $check_stmt->close();
    closeConnection($connection);
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-truck me-2"></i>Master Supplier
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalSuplier">
                <i class="fas fa-plus me-2"></i>Tambah Supplier
            </button>
        </div>
    </div>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Data Supplier</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="tableSuplier" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="8%">No</th>
                                <th width="25%">Nama Supplier</th>
                                <th width="35%">Alamat</th>
                                <th width="17%">Telepon</th>
                                <th width="15%">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via DataTables AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Supplier -->
<div class="modal fade" id="modalSuplier" tabindex="-1" aria-labelledby="modalSuplierLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalSuplierLabel">
                    <i class="fas fa-truck me-2"></i>Tambah Supplier
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formSuplier">
                <div class="modal-body">
                    <input type="hidden" id="kd_suplier" name="kd_suplier">
                    <input type="hidden" id="action" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nama_suplier" class="form-label">Nama Supplier <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nama_suplier" name="nama_suplier" required maxlength="50">
                                <div class="invalid-feedback">
                                    Nama supplier harus diisi.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tlp_suplier" class="form-label">Telepon <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="tlp_suplier" name="tlp_suplier" required maxlength="20" placeholder="Contoh: 081234567890">
                                <div class="invalid-feedback">
                                    Telepon harus diisi.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="alamat_suplier" class="form-label">Alamat <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="alamat_suplier" name="alamat_suplier" rows="3" required maxlength="100" placeholder="Masukkan alamat lengkap supplier"></textarea>
                        <div class="invalid-feedback">
                            Alamat harus diisi.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="btnSimpan">
                        <i class="fas fa-save me-2"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$page_scripts = ['suplier/js/suplier.js'];
require_once '../includes/footer.php';
?>
