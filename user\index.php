<?php
$page_title = 'Master User';
require_once '../config/database.php';
require_once '../includes/header.php';

// Check if user is admin
requireAdmin();

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $kd_user = (int)$_GET['id'];
    $current_user = getCurrentUser();
    
    // Prevent deleting own account
    if ($kd_user == $current_user['kd_user']) {
        $error_message = "Anda tidak dapat menghapus akun Anda sendiri.";
    } else {
        $connection = getConnection();
        
        // Check if user is used in transactions
        $check_query = "SELECT 
                            (SELECT COUNT(*) FROM pembelian WHERE kd_user = ?) +
                            (SELECT COUNT(*) FROM penjualan WHERE kd_user = ?) +
                            (SELECT COUNT(*) FROM retur_pembelian WHERE kd_user = ?) as count";
        $check_stmt = $connection->prepare($check_query);
        $check_stmt->bind_param("iii", $kd_user, $kd_user, $kd_user);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        $count = $check_result->fetch_assoc()['count'];
        
        if ($count > 0) {
            $error_message = "User tidak dapat dihapus karena masih digunakan pada $count transaksi.";
        } else {
            $delete_query = "DELETE FROM user WHERE kd_user = ?";
            $delete_stmt = $connection->prepare($delete_query);
            $delete_stmt->bind_param("i", $kd_user);
            
            if ($delete_stmt->execute()) {
                $success_message = "User berhasil dihapus.";
            } else {
                $error_message = "Gagal menghapus user.";
            }
            $delete_stmt->close();
        }
        
        $check_stmt->close();
        closeConnection($connection);
    }
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2"></i>Master User
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalUser">
                <i class="fas fa-plus me-2"></i>Tambah User
            </button>
        </div>
    </div>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Data User</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="tableUser" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="8%">No</th>
                                <th width="15%">NIK</th>
                                <th width="25%">Nama User</th>
                                <th width="10%">JK</th>
                                <th width="17%">Username</th>
                                <th width="10%">Level</th>
                                <th width="15%">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via DataTables AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal User -->
<div class="modal fade" id="modalUser" tabindex="-1" aria-labelledby="modalUserLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalUserLabel">
                    <i class="fas fa-users me-2"></i>Tambah User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formUser">
                <div class="modal-body">
                    <input type="hidden" id="kd_user" name="kd_user">
                    <input type="hidden" id="action" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nik" class="form-label">NIK <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nik" name="nik" required maxlength="30" placeholder="Nomor Induk Karyawan">
                                <div class="invalid-feedback">
                                    NIK harus diisi.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nama_user" class="form-label">Nama User <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nama_user" name="nama_user" required maxlength="50" placeholder="Nama lengkap user">
                                <div class="invalid-feedback">
                                    Nama user harus diisi.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jk" class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                <select class="form-select" id="jk" name="jk" required>
                                    <option value="">Pilih Jenis Kelamin</option>
                                    <option value="Laki-laki">Laki-laki</option>
                                    <option value="Perempuan">Perempuan</option>
                                </select>
                                <div class="invalid-feedback">
                                    Jenis kelamin harus dipilih.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="level" class="form-label">Level <span class="text-danger">*</span></label>
                                <select class="form-select" id="level" name="level" required>
                                    <option value="">Pilih Level</option>
                                    <option value="admin">Admin</option>
                                    <option value="user">User</option>
                                </select>
                                <div class="invalid-feedback">
                                    Level harus dipilih.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required maxlength="20" placeholder="Username untuk login">
                                <div class="invalid-feedback">
                                    Username harus diisi.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password <span class="text-danger" id="password-required">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" maxlength="50" placeholder="Password untuk login">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Password harus diisi.
                                </div>
                                <div class="form-text" id="password-help">
                                    Kosongkan jika tidak ingin mengubah password.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="btnSimpan">
                        <i class="fas fa-save me-2"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$page_scripts = ['user/js/user.js'];
require_once '../includes/footer.php';
?>
