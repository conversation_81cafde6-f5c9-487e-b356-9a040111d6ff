<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Check if user is admin
requireAdmin();

$connection = getConnection();
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            $nik = trim($_POST['nik'] ?? '');
            $nama_user = trim($_POST['nama_user'] ?? '');
            $jk = trim($_POST['jk'] ?? '');
            $username = trim($_POST['username'] ?? '');
            $password = trim($_POST['password'] ?? '');
            $level = trim($_POST['level'] ?? '');
            
            // Validation
            if (empty($nik)) {
                $response['message'] = 'NIK harus diisi.';
                break;
            }
            
            if (empty($nama_user)) {
                $response['message'] = 'Nama user harus diisi.';
                break;
            }
            
            if (empty($jk) || !in_array($jk, ['Laki-laki', 'Perempuan'])) {
                $response['message'] = 'Jenis kelamin harus dipilih.';
                break;
            }
            
            if (empty($username)) {
                $response['message'] = 'Username harus diisi.';
                break;
            }
            
            if (empty($password)) {
                $response['message'] = 'Password harus diisi.';
                break;
            }
            
            if (empty($level) || !in_array($level, ['admin', 'user'])) {
                $response['message'] = 'Level harus dipilih.';
                break;
            }
            
            // Check if NIK already exists
            $check_query = "SELECT kd_user FROM user WHERE nik = ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("s", $nik);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'NIK sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Check if username already exists
            $check_query = "SELECT kd_user FROM user WHERE username = ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("s", $username);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Username sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Hash password with MD5
            $hashed_password = md5($password);
            
            // Insert new user
            $insert_query = "INSERT INTO user (nik, nama_user, jk, username, password, level) VALUES (?, ?, ?, ?, ?, ?)";
            $insert_stmt = $connection->prepare($insert_query);
            $insert_stmt->bind_param("ssssss", $nik, $nama_user, $jk, $username, $hashed_password, $level);
            
            if ($insert_stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'User berhasil ditambahkan.';
            } else {
                $response['message'] = 'Gagal menambahkan user.';
            }
            $insert_stmt->close();
            break;
            
        case 'edit':
            $kd_user = (int)($_POST['kd_user'] ?? 0);
            $nik = trim($_POST['nik'] ?? '');
            $nama_user = trim($_POST['nama_user'] ?? '');
            $jk = trim($_POST['jk'] ?? '');
            $username = trim($_POST['username'] ?? '');
            $password = trim($_POST['password'] ?? '');
            $level = trim($_POST['level'] ?? '');
            
            // Validation
            if (empty($nik)) {
                $response['message'] = 'NIK harus diisi.';
                break;
            }
            
            if (empty($nama_user)) {
                $response['message'] = 'Nama user harus diisi.';
                break;
            }
            
            if (empty($jk) || !in_array($jk, ['Laki-laki', 'Perempuan'])) {
                $response['message'] = 'Jenis kelamin harus dipilih.';
                break;
            }
            
            if (empty($username)) {
                $response['message'] = 'Username harus diisi.';
                break;
            }
            
            if (empty($level) || !in_array($level, ['admin', 'user'])) {
                $response['message'] = 'Level harus dipilih.';
                break;
            }
            
            if ($kd_user <= 0) {
                $response['message'] = 'ID user tidak valid.';
                break;
            }
            
            // Check if NIK already exists (except current record)
            $check_query = "SELECT kd_user FROM user WHERE nik = ? AND kd_user != ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("si", $nik, $kd_user);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'NIK sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Check if username already exists (except current record)
            $check_query = "SELECT kd_user FROM user WHERE username = ? AND kd_user != ?";
            $check_stmt = $connection->prepare($check_query);
            $check_stmt->bind_param("si", $username, $kd_user);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $response['message'] = 'Username sudah ada.';
                $check_stmt->close();
                break;
            }
            $check_stmt->close();
            
            // Update user
            if (!empty($password)) {
                // Update with new password
                $hashed_password = md5($password);
                $update_query = "UPDATE user SET nik = ?, nama_user = ?, jk = ?, username = ?, password = ?, level = ? WHERE kd_user = ?";
                $update_stmt = $connection->prepare($update_query);
                $update_stmt->bind_param("ssssssi", $nik, $nama_user, $jk, $username, $hashed_password, $level, $kd_user);
            } else {
                // Update without changing password
                $update_query = "UPDATE user SET nik = ?, nama_user = ?, jk = ?, username = ?, level = ? WHERE kd_user = ?";
                $update_stmt = $connection->prepare($update_query);
                $update_stmt->bind_param("sssssi", $nik, $nama_user, $jk, $username, $level, $kd_user);
            }
            
            if ($update_stmt->execute()) {
                if ($update_stmt->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = 'User berhasil diperbarui.';
                } else {
                    $response['message'] = 'Tidak ada perubahan data.';
                }
            } else {
                $response['message'] = 'Gagal memperbarui user.';
            }
            $update_stmt->close();
            break;
            
        case 'get':
            $kd_user = (int)($_POST['kd_user'] ?? 0);
            
            if ($kd_user <= 0) {
                $response['message'] = 'ID user tidak valid.';
                break;
            }
            
            $query = "SELECT kd_user, nik, nama_user, jk, username, level FROM user WHERE kd_user = ?";
            $stmt = $connection->prepare($query);
            $stmt->bind_param("i", $kd_user);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $response['success'] = true;
                $response['data'] = $result->fetch_assoc();
            } else {
                $response['message'] = 'User tidak ditemukan.';
            }
            $stmt->close();
            break;
            
        default:
            $response['message'] = 'Aksi tidak valid.';
            break;
    }
}

// Handle DataTables server-side processing
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['draw'])) {
    $draw = (int)$_GET['draw'];
    $start = (int)$_GET['start'];
    $length = (int)$_GET['length'];
    $search = $_GET['search']['value'] ?? '';
    $current_user = getCurrentUser();
    
    // Base query
    $base_query = "FROM user";
    $where_clause = "";
    $params = [];
    $param_types = "";
    
    // Search functionality
    if (!empty($search)) {
        $where_clause = " WHERE nik LIKE ? OR nama_user LIKE ? OR username LIKE ? OR level LIKE ?";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $param_types .= "ssss";
    }
    
    // Count total records
    $count_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    $count_stmt = $connection->prepare($count_query);
    if (!empty($params)) {
        $count_stmt->bind_param($param_types, ...$params);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
    
    // Get data with pagination
    $data_query = "SELECT kd_user, nik, nama_user, jk, username, level " . $base_query . $where_clause . " ORDER BY nama_user ASC LIMIT ?, ?";
    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";
    
    $data_stmt = $connection->prepare($data_query);
    $data_stmt->bind_param($param_types, ...$params);
    $data_stmt->execute();
    $result = $data_stmt->get_result();
    
    $data = [];
    $no = $start + 1;
    while ($row = $result->fetch_assoc()) {
        $level_badge = $row['level'] == 'admin' ? 'bg-danger' : 'bg-primary';
        $is_current_user = $row['kd_user'] == $current_user['kd_user'];
        
        $actions = '<div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-warning btn-edit" data-id="' . $row['kd_user'] . '" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>';
        
        if (!$is_current_user) {
            $actions .= '<a href="?action=delete&id=' . $row['kd_user'] . '" class="btn btn-sm btn-danger btn-delete" title="Hapus" data-message="Apakah Anda yakin ingin menghapus user ' . htmlspecialchars($row['nama_user']) . '?">
                            <i class="fas fa-trash"></i>
                        </a>';
        }
        
        $actions .= '</div>';
        
        $data[] = [
            $no++,
            htmlspecialchars($row['nik']),
            htmlspecialchars($row['nama_user']) . ($is_current_user ? ' <span class="badge bg-success">You</span>' : ''),
            htmlspecialchars($row['jk']),
            htmlspecialchars($row['username']),
            '<span class="badge ' . $level_badge . '">' . ucfirst($row['level']) . '</span>',
            $actions
        ];
    }
    $data_stmt->close();
    
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ];
}

closeConnection($connection);

header('Content-Type: application/json');
echo json_encode($response);
?>
