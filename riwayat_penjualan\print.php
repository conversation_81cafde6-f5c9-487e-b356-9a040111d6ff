<?php
require_once '../config/database.php';
require_once '../config/session.php';

// Check if user is logged in
requireLogin();

$kd_penjualan = (int)($_GET['kd_penjualan'] ?? 0);

if ($kd_penjualan <= 0) {
    die('ID transaksi tidak valid.');
}

$connection = getConnection();

// Get transaction header
$header_query = "SELECT p.*, pl.nama_pelanggan, u.nama_user
                FROM penjualan p
                LEFT JOIN pelanggan pl ON p.kd_pelanggan = pl.kd_pelanggan
                LEFT JOIN users u ON p.kd_user = u.kd_user
                WHERE p.kd_penjualan = ?";
$stmt = $connection->prepare($header_query);
$stmt->bind_param("i", $kd_penjualan);
$stmt->execute();
$header = $stmt->get_result()->fetch_assoc();
$stmt->close();

if (!$header) {
    die('Transaksi tidak ditemukan.');
}

// Get transaction items
$items_query = "SELECT pi.*, b.nama_barang, b.barcode, s.nama_satuan
               FROM penjualan_item pi
               JOIN barang b ON pi.kd_barang = b.kd_barang
               LEFT JOIN satuan s ON b.kd_satuan = s.kd_satuan
               WHERE pi.kd_penjualan = ?
               ORDER BY pi.kd_barang";
$stmt = $connection->prepare($items_query);
$stmt->bind_param("i", $kd_penjualan);
$stmt->execute();
$items_result = $stmt->get_result();

$items = [];
while ($item = $items_result->fetch_assoc()) {
    $items[] = $item;
}
$stmt->close();

closeConnection($connection);

// Calculate totals
$subtotal = 0;
$total_diskon = 0;

foreach ($items as $item) {
    $item_subtotal = $item['harga_jual'] * $item['jumlah'];
    $item_diskon = ($item_subtotal * $item['disc']) / 100;
    
    $subtotal += $item_subtotal;
    $total_diskon += $item_diskon;
}

$grand_total = $subtotal - $total_diskon;
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk Penjualan - <?php echo $header['no_transaksi']; ?></title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
            margin: 0;
            padding: 10px;
            width: 80mm;
            max-width: 80mm;
        }
        
        .header {
            text-align: center;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .header h2 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }
        
        .header p {
            margin: 2px 0;
            font-size: 10px;
        }
        
        .transaction-info {
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        
        .transaction-info div {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
        }
        
        .items {
            margin-bottom: 10px;
        }
        
        .item {
            margin-bottom: 8px;
            border-bottom: 1px dotted #ccc;
            padding-bottom: 5px;
        }
        
        .item-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .item-details {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }
        
        .totals {
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .totals div {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
        }
        
        .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        
        .payment-info {
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .payment-info div {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 15px;
            border-top: 1px dashed #000;
            padding-top: 10px;
            font-size: 10px;
        }
        
        .print-button {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="print-button no-print">
        <button class="btn" onclick="window.print()">
            <i class="fas fa-print"></i> Print Struk
        </button>
        <button class="btn" onclick="window.close()" style="background-color: #6c757d;">
            <i class="fas fa-times"></i> Tutup
        </button>
    </div>

    <div class="receipt">
        <div class="header">
            <h2>KOPEGBI</h2>
            <p>Jl. Contoh No. 123</p>
            <p>Telp: (021) 1234567</p>
        </div>

        <div class="transaction-info">
            <div>
                <span>No. Transaksi:</span>
                <span><?php echo $header['no_transaksi']; ?></span>
            </div>
            <div>
                <span>Tanggal:</span>
                <span><?php echo date('d/m/Y H:i', strtotime($header['tgl_penjualan'])); ?></span>
            </div>
            <div>
                <span>Kasir:</span>
                <span><?php echo $header['nama_user']; ?></span>
            </div>
            <div>
                <span>Pelanggan:</span>
                <span><?php echo $header['nama_pelanggan'] ?: 'Umum'; ?></span>
            </div>
        </div>

        <div class="items">
            <?php foreach ($items as $item): 
                $item_subtotal = $item['harga_jual'] * $item['jumlah'];
                $item_diskon = ($item_subtotal * $item['disc']) / 100;
                $item_total = $item_subtotal - $item_diskon;
            ?>
                <div class="item">
                    <div class="item-name"><?php echo $item['nama_barang']; ?></div>
                    <div class="item-details">
                        <span><?php echo $item['jumlah']; ?> x <?php echo number_format($item['harga_jual'], 0, ',', '.'); ?></span>
                        <span><?php echo number_format($item_total, 0, ',', '.'); ?></span>
                    </div>
                    <?php if ($item['disc'] > 0): ?>
                        <div class="item-details">
                            <span>Diskon <?php echo $item['disc']; ?>%</span>
                            <span>-<?php echo number_format($item_diskon, 0, ',', '.'); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="totals">
            <div>
                <span>Subtotal:</span>
                <span>Rp <?php echo number_format($subtotal, 0, ',', '.'); ?></span>
            </div>
            <?php if ($total_diskon > 0): ?>
                <div>
                    <span>Total Diskon:</span>
                    <span>-Rp <?php echo number_format($total_diskon, 0, ',', '.'); ?></span>
                </div>
            <?php endif; ?>
            <div class="grand-total">
                <span>TOTAL:</span>
                <span>Rp <?php echo number_format($grand_total, 0, ',', '.'); ?></span>
            </div>
        </div>

        <div class="payment-info">
            <div>
                <span>Cara Bayar:</span>
                <span><?php echo $header['cara_bayar']; ?></span>
            </div>
            <div>
                <span>Dibayar:</span>
                <span>Rp <?php echo number_format($header['dibayar'], 0, ',', '.'); ?></span>
            </div>
            <div>
                <span>Kembalian:</span>
                <span>Rp <?php echo number_format($header['kembalian'], 0, ',', '.'); ?></span>
            </div>
        </div>

        <?php if ($header['keterangan']): ?>
            <div style="margin-top: 10px; border-top: 1px dashed #000; padding-top: 10px;">
                <strong>Keterangan:</strong><br>
                <?php echo nl2br(htmlspecialchars($header['keterangan'])); ?>
            </div>
        <?php endif; ?>

        <div class="footer">
            <p>Terima kasih atas kunjungan Anda!</p>
            <p>Barang yang sudah dibeli tidak dapat dikembalikan</p>
            <p>Simpan struk ini sebagai bukti pembelian</p>
        </div>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            // Small delay to ensure page is fully loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
